#!/usr/bin/env python3
"""
RecoveryStand 状态诊断脚本
分析为什么机器人无法进入 StandUp 状态
"""

import re
import sys
from collections import defaultdict

class RecoveryStandDiagnostic:
    def __init__(self):
        self.state_transitions = []
        self.upside_down_checks = []
        self.fold_legs_completions = []
        self.rollover_completions = []
        
    def parse_log_line(self, line):
        """解析日志行"""
        
        # 解析状态标志
        flag_match = re.search(r'\[RecoveryStand\] Current flag: (\w+) \(Iteration: (\d+)\)', line)
        if flag_match:
            flag, iteration = flag_match.groups()
            self.state_transitions.append({
                'flag': flag,
                'iteration': int(iteration)
            })
        
        # 解析倒置检查
        upside_match = re.search(r'\[UpsideDown Check\] rBody\(2,2\) = ([-\d.]+), UpsideDown = (\w+)', line)
        if upside_match:
            rbody_zz, is_upside = upside_match.groups()
            self.upside_down_checks.append({
                'rbody_zz': float(rbody_zz),
                'is_upside': is_upside == 'TRUE'
            })
        
        # 解析 FoldLegs 完成
        fold_complete_match = re.search(r'\[FoldLegs Complete\] UpsideDown: (\w+), Body Height: ([-\d.]+)', line)
        if fold_complete_match:
            upside, height = fold_complete_match.groups()
            self.fold_legs_completions.append({
                'upside_down': upside == 'TRUE',
                'body_height': float(height)
            })
        
        # 解析 RollOver 完成
        rollover_match = re.search(r'\[RollOver Complete\] Rollover finished, rBody\(2,2\) = ([-\d.]+)', line)
        if rollover_match:
            rbody_zz = rollover_match.groups()[0]
            self.rollover_completions.append({
                'rbody_zz': float(rbody_zz)
            })
    
    def analyze_log_file(self, filename):
        """分析日志文件"""
        with open(filename, 'r') as f:
            for line in f:
                self.parse_log_line(line.strip())
    
    def analyze_state_pattern(self):
        """分析状态模式"""
        print("=== 状态转换分析 ===")
        
        if not self.state_transitions:
            print("❌ 没有找到状态转换信息")
            return
        
        # 统计状态分布
        state_counts = defaultdict(int)
        for transition in self.state_transitions:
            state_counts[transition['flag']] += 1
        
        print(f"状态分布:")
        for state, count in state_counts.items():
            print(f"  {state}: {count} 次")
        
        # 检查是否进入过 StandUp
        standup_entries = [t for t in self.state_transitions if t['flag'] == 'StandUp']
        if standup_entries:
            print(f"✅ 检测到 {len(standup_entries)} 次 StandUp 状态")
        else:
            print("❌ 从未进入 StandUp 状态")
        
        # 分析状态循环
        recent_states = [t['flag'] for t in self.state_transitions[-10:]]
        if len(set(recent_states)) <= 2 and 'StandUp' not in recent_states:
            print("❌ 检测到状态循环，未能进入 StandUp")
            print(f"   最近状态: {' -> '.join(recent_states)}")
    
    def analyze_upside_down_detection(self):
        """分析倒置检测"""
        print("\n=== 倒置检测分析 ===")
        
        if not self.upside_down_checks:
            print("❌ 没有找到倒置检测信息")
            return
        
        # 分析 rBody(2,2) 值
        rbody_values = [check['rbody_zz'] for check in self.upside_down_checks]
        upside_detections = [check['is_upside'] for check in self.upside_down_checks]
        
        print(f"rBody(2,2) 值范围: {min(rbody_values):.4f} 到 {max(rbody_values):.4f}")
        print(f"平均值: {sum(rbody_values)/len(rbody_values):.4f}")
        
        upside_count = sum(upside_detections)
        print(f"倒置检测: {upside_count}/{len(upside_detections)} 次")
        
        # 检查阈值问题
        if max(rbody_values) < -0.1:
            print("⚠️  所有 rBody(2,2) 值都 < -0.1，可能一直被判定为倒置")
        elif min(rbody_values) > -0.1:
            print("✅ 所有 rBody(2,2) 值都 > -0.1，应该不会被判定为倒置")
        else:
            print("📊 rBody(2,2) 值在阈值附近波动")
    
    def analyze_fold_legs_decisions(self):
        """分析 FoldLegs 决策"""
        print("\n=== FoldLegs 决策分析 ===")
        
        if not self.fold_legs_completions:
            print("❌ 没有找到 FoldLegs 完成信息")
            return
        
        for i, completion in enumerate(self.fold_legs_completions):
            print(f"FoldLegs 完成 #{i+1}:")
            print(f"  倒置检测: {completion['upside_down']}")
            print(f"  身体高度: {completion['body_height']:.3f}m")
            
            if completion['upside_down']:
                print("  → 决策: 进入 RollOver")
            else:
                print("  → 决策: 进入 StandUp")
    
    def analyze_rollover_effectiveness(self):
        """分析翻身效果"""
        print("\n=== 翻身效果分析 ===")
        
        if not self.rollover_completions:
            print("❌ 没有找到 RollOver 完成信息")
            return
        
        for i, completion in enumerate(self.rollover_completions):
            rbody_after = completion['rbody_zz']
            print(f"RollOver 完成 #{i+1}:")
            print(f"  翻身后 rBody(2,2): {rbody_after:.4f}")
            
            if rbody_after > -0.1:
                print("  ✅ 翻身成功，应该不再被判定为倒置")
            else:
                print("  ❌ 翻身后仍然 < -0.1，可能仍被判定为倒置")
    
    def generate_recommendations(self):
        """生成建议"""
        print("\n=== 诊断建议 ===")
        
        # 检查是否从未进入 StandUp
        standup_entries = [t for t in self.state_transitions if t['flag'] == 'StandUp']
        if not standup_entries:
            print("🔴 主要问题: 机器人从未进入 StandUp 状态")
            
            # 检查倒置检测
            if self.upside_down_checks:
                always_upside = all(check['is_upside'] for check in self.upside_down_checks)
                if always_upside:
                    print("🔍 根本原因: 倒置检测一直返回 TRUE")
                    print("💡 建议:")
                    print("   1. 检查状态估计器是否正常工作")
                    print("   2. 调整倒置检测阈值 (当前 -0.1)")
                    print("   3. 临时强制进入 StandUp 进行测试")
                    
                    # 检查 rBody 值
                    rbody_values = [check['rbody_zz'] for check in self.upside_down_checks]
                    avg_rbody = sum(rbody_values) / len(rbody_values)
                    if avg_rbody < -0.8:
                        print("   4. rBody(2,2) 平均值很低，可能机器人确实倒置")
                    elif avg_rbody > -0.2:
                        print("   4. rBody(2,2) 接近阈值，建议调整阈值到 -0.3")
            
            # 检查翻身效果
            if self.rollover_completions:
                ineffective_rollovers = [r for r in self.rollover_completions if r['rbody_zz'] < -0.1]
                if ineffective_rollovers:
                    print("🔍 翻身动作无效")
                    print("💡 建议:")
                    print("   1. 检查翻身动作的关节角度设置")
                    print("   2. 增加翻身动作的幅度")
                    print("   3. 检查机器人是否真的在地面上")
        
        # 检查状态循环
        if len(self.state_transitions) > 10:
            recent_flags = [t['flag'] for t in self.state_transitions[-10:]]
            unique_flags = set(recent_flags)
            if len(unique_flags) <= 2 and 'StandUp' not in unique_flags:
                print("🔴 检测到状态循环")
                print("💡 建议: 添加循环检测和强制退出机制")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 diagnose_recovery_stand.py <log_file>")
        return
    
    diagnostic = RecoveryStandDiagnostic()
    
    try:
        diagnostic.analyze_log_file(sys.argv[1])
        diagnostic.analyze_state_pattern()
        diagnostic.analyze_upside_down_detection()
        diagnostic.analyze_fold_legs_decisions()
        diagnostic.analyze_rollover_effectiveness()
        diagnostic.generate_recommendations()
    except FileNotFoundError:
        print(f"错误: 找不到文件 {sys.argv[1]}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
