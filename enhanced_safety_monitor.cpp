/**
 * 增强的安全监控系统示例
 * 展示如何在 locomotion 状态下实现全面的安全检查
 */

#include "enhanced_safety_monitor.h"
#include <iostream>
#include <cmath>

template <typename T>
class EnhancedSafetyMonitor {
private:
    ControlFSMData<T>* _data;
    
    // 安全阈值参数
    struct SafetyThresholds {
        T max_roll = 40.0;           // 度
        T max_pitch = 40.0;          // 度
        T max_leg_velocity = 9.0;    // m/s
        T max_lateral_force = 350.0; // N (Mini Cheetah)
        T max_vertical_force = 350.0; // N
        T min_leg_height = -0.4;     // m (相对髋关节)
        T max_leg_extension = 0.35;  // m
    } thresholds;
    
    // 安全状态统计
    struct SafetyStats {
        int orientation_violations = 0;
        int velocity_violations = 0;
        int force_violations = 0;
        int position_violations = 0;
        int consecutive_safe_cycles = 0;
    } stats;

public:
    EnhancedSafetyMonitor(ControlFSMData<T>* data) : _data(data) {}
    
    /**
     * 主安全检查函数
     * @return true if all safety checks pass
     */
    bool comprehensiveSafetyCheck() {
        bool all_safe = true;
        
        // 1. 姿态安全检查
        if (!checkOrientation()) {
            all_safe = false;
            stats.orientation_violations++;
        }
        
        // 2. 腿部运动学检查
        if (!checkLegKinematics()) {
            all_safe = false;
            stats.position_violations++;
        }
        
        // 3. 速度安全检查
        if (!checkVelocities()) {
            all_safe = false;
            stats.velocity_violations++;
        }
        
        // 4. 力矩安全检查
        if (!checkForces()) {
            all_safe = false;
            stats.force_violations++;
        }
        
        // 5. 系统稳定性检查
        if (!checkSystemStability()) {
            all_safe = false;
        }
        
        // 更新统计
        if (all_safe) {
            stats.consecutive_safe_cycles++;
        } else {
            stats.consecutive_safe_cycles = 0;
        }
        
        return all_safe;
    }
    
    /**
     * 检查机器人姿态
     */
    bool checkOrientation() {
        auto& seResult = _data->_stateEstimator->getResult();
        
        T roll_deg = seResult.rpy(0) * 180.0 / M_PI;
        T pitch_deg = seResult.rpy(1) * 180.0 / M_PI;
        
        if (std::abs(roll_deg) > thresholds.max_roll) {
            printf("[SAFETY] Orientation FAIL: Roll %.2f° > %.2f°\n", 
                   roll_deg, thresholds.max_roll);
            return false;
        }
        
        if (std::abs(pitch_deg) > thresholds.max_pitch) {
            printf("[SAFETY] Orientation FAIL: Pitch %.2f° > %.2f°\n", 
                   pitch_deg, thresholds.max_pitch);
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查腿部运动学
     */
    bool checkLegKinematics() {
        bool safe = true;
        
        for (int leg = 0; leg < 4; leg++) {
            auto p_leg = _data->_legController->datas[leg].p;
            
            // 检查腿部高度
            if (p_leg[2] > 0) {
                printf("[SAFETY] Leg %d above hip: %.3f m\n", leg, p_leg[2]);
                safe = false;
            }
            
            // 检查腿部伸展
            T leg_extension = sqrt(p_leg[0]*p_leg[0] + p_leg[1]*p_leg[1] + p_leg[2]*p_leg[2]);
            if (leg_extension > thresholds.max_leg_extension) {
                printf("[SAFETY] Leg %d over-extended: %.3f m > %.3f m\n", 
                       leg, leg_extension, thresholds.max_leg_extension);
                safe = false;
            }
            
            // 检查Y方向位置（避免腿部交叉）
            if (std::abs(p_leg[1]) > 0.18) {
                printf("[SAFETY] Leg %d Y-position unsafe: %.3f m\n", leg, p_leg[1]);
                safe = false;
            }
        }
        
        return safe;
    }
    
    /**
     * 检查速度安全
     */
    bool checkVelocities() {
        bool safe = true;
        auto& seResult = _data->_stateEstimator->getResult();
        
        // 检查身体速度
        T body_velocity = seResult.vBody.norm();
        if (body_velocity > 5.0) {  // m/s
            printf("[SAFETY] Body velocity too high: %.3f m/s\n", body_velocity);
            safe = false;
        }
        
        // 检查腿部速度
        for (int leg = 0; leg < 4; leg++) {
            auto v_leg = _data->_legController->datas[leg].v.norm();
            if (v_leg > thresholds.max_leg_velocity) {
                printf("[SAFETY] Leg %d velocity too high: %.3f m/s\n", leg, v_leg);
                safe = false;
            }
        }
        
        return safe;
    }
    
    /**
     * 检查力矩安全
     */
    bool checkForces() {
        bool safe = true;
        
        for (int leg = 0; leg < 4; leg++) {
            auto& cmd = _data->_legController->commands[leg];
            
            // 检查前馈力
            for (int axis = 0; axis < 3; axis++) {
                T force_mag = std::abs(cmd.forceFeedForward(axis));
                T max_force = (axis == 2) ? thresholds.max_vertical_force : thresholds.max_lateral_force;
                
                if (force_mag > max_force) {
                    printf("[SAFETY] Leg %d axis %d force too high: %.1f N > %.1f N\n", 
                           leg, axis, force_mag, max_force);
                    
                    // 自动限制力矩
                    cmd.forceFeedForward(axis) = (cmd.forceFeedForward(axis) > 0) ? max_force : -max_force;
                    safe = false;
                }
            }
            
            // 检查关节力矩
            for (int joint = 0; joint < 3; joint++) {
                T torque_mag = std::abs(_data->_legController->datas[leg].tauEstimate[joint]);
                T max_torque = (joint == 2) ? 15.0 : 20.0;  // 膝关节更保守
                
                if (torque_mag > max_torque) {
                    printf("[SAFETY] Leg %d joint %d torque too high: %.1f Nm > %.1f Nm\n", 
                           leg, joint, torque_mag, max_torque);
                    safe = false;
                }
            }
        }
        
        return safe;
    }
    
    /**
     * 检查系统稳定性
     */
    bool checkSystemStability() {
        auto& seResult = _data->_stateEstimator->getResult();
        
        // 检查角速度
        T angular_velocity = seResult.omegaBody.norm();
        if (angular_velocity > 10.0) {  // rad/s
            printf("[SAFETY] Angular velocity too high: %.3f rad/s\n", angular_velocity);
            return false;
        }
        
        // 检查接触状态
        int contact_count = 0;
        for (int leg = 0; leg < 4; leg++) {
            if (_data->_legController->datas[leg].tauEstimate[2] < -5.0) {  // 简单的接触检测
                contact_count++;
            }
        }
        
        if (contact_count < 2) {
            printf("[SAFETY] Insufficient ground contact: %d legs\n", contact_count);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取安全状态报告
     */
    void printSafetyReport() {
        printf("\n=== Safety Monitor Report ===\n");
        printf("Orientation violations: %d\n", stats.orientation_violations);
        printf("Velocity violations: %d\n", stats.velocity_violations);
        printf("Force violations: %d\n", stats.force_violations);
        printf("Position violations: %d\n", stats.position_violations);
        printf("Consecutive safe cycles: %d\n", stats.consecutive_safe_cycles);
        printf("============================\n\n");
    }
    
    /**
     * 紧急安全响应
     */
    void emergencyResponse() {
        printf("[SAFETY] EMERGENCY RESPONSE ACTIVATED!\n");
        
        // 1. 立即降低所有力矩命令
        for (int leg = 0; leg < 4; leg++) {
            _data->_legController->commands[leg].forceFeedForward.setZero();
            _data->_legController->commands[leg].kpJoint *= 0.5;  // 降低增益
            _data->_legController->commands[leg].kdJoint *= 1.5;  // 增加阻尼
        }
        
        // 2. 设置安全的关节位置目标
        for (int leg = 0; leg < 4; leg++) {
            _data->_legController->commands[leg].qDes << 0.0, 0.8, -1.6;  // 安全蹲姿
            _data->_legController->commands[leg].qdDes.setZero();
        }
        
        printf("[SAFETY] Emergency commands applied\n");
    }
};

// 使用示例
template <typename T>
void locomotionSafetyExample(ControlFSMData<T>* data) {
    EnhancedSafetyMonitor<T> safety_monitor(data);
    
    // 在控制循环中使用
    if (!safety_monitor.comprehensiveSafetyCheck()) {
        printf("[LOCOMOTION] Safety check failed!\n");
        
        // 触发紧急响应
        safety_monitor.emergencyResponse();
        
        // 请求状态转换到恢复模式
        data->controlParameters->control_mode = K_RECOVERY_STAND;
    }
    
    // 定期打印安全报告
    static int report_counter = 0;
    if (++report_counter % 1000 == 0) {  // 每2秒打印一次 (500Hz控制频率)
        safety_monitor.printSafetyReport();
    }
}

// 模板实例化
template class EnhancedSafetyMonitor<float>;
