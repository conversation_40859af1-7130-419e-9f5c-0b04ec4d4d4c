# RecoveryStand 状态问题修复方案

## 问题分析

### 1. 循环蹲下-站立问题
**根本原因**: 
- `_StandUp` 方法中的身体高度检测过于敏感 (body_height < 0.1)
- 检测时机过早 (standup_ramp_iter*0.7)，导致状态机频繁重启

### 2. 小腿力矩过大问题
**根本原因**:
- 膝关节目标位置 `-1.21763` rad 可能接近机械限位
- 关节PD控制器增益过高，特别是膝关节
- 膝关节力矩达到 45.43 Nm，远超正常值

## 修复方案

### 1. 调整站立目标位置
```cpp
// 原始目标 (可能导致过度伸展)
stand_jpos[i] << ±0.00571868f, 0.608813f, -1.21763f;

// 修复后 (更安全的膝关节角度)  
stand_jpos[i] << ±0.00571868f, 0.608813f, -1.5f;
```

### 2. 修复状态机逻辑
```cpp
// 原始检测条件 (过于敏感)
if( _UpsideDown() || (body_height < 0.1 ) ) 
if( (curr_iter > floor(standup_ramp_iter*0.7) ) && something_wrong)

// 修复后 (更保守的检测)
if( _UpsideDown() || (body_height < 0.05 ) )  // 更低的高度阈值
if( (curr_iter > floor(standup_ramp_iter*0.9) ) && something_wrong)  // 更晚的检测时机
```

### 3. 添加站立完成逻辑
```cpp
// 新增：站立动作完成后保持位置
if(curr_iter >= standup_ramp_iter + standup_settle_iter) {
  // 保持站立位置，不再重启状态机
  for(size_t leg(0); leg<4; ++leg){
    this->_recoveryJointPDControl(leg, stand_jpos[leg], zero_vec3);
  }
}
```

### 4. 降低关节PD增益
```cpp
// 新增专用的恢复控制方法
void _recoveryJointPDControl(int leg, Vec3<T> qDes, Vec3<T> qdDes) {
  kpMat << 15, 0, 0,    // Abad: 降低增益
           0, 15, 0,    // Hip: 降低增益  
           0, 0, 8;     // Knee: 大幅降低增益 (原来20->8)
           
  kdMat << 2.0, 0, 0,   // 适中的阻尼
           0, 2.0, 0,   
           0, 0, 1.5;   // 膝关节增加阻尼
}
```

### 5. 增强调试监控
```cpp
// 每500次迭代输出状态信息
if(_state_iter % 500 == 0) {
  printf("[RecoveryStand] Flag: %s, Iter: %llu, Motion Iter: %llu, Body Height: %.3f\n", 
         flag_names[_flag], _state_iter, _state_iter - _motion_start_iter, body_height);
}
```

## 预期效果

### 1. 解决循环问题
- 机器人将完成站立动作后保持稳定站立
- 不再频繁重启状态机
- 状态转换更加稳定

### 2. 降低力矩
- 膝关节力矩从 45+ Nm 降低到合理范围 (< 20 Nm)
- 减少电机过热和机械磨损
- 提高控制稳定性

### 3. 改善调试
- 清晰的状态转换日志
- 实时的身体高度监控
- 便于问题诊断

## 测试建议

### 1. 安全测试
```bash
# 编译项目
make clean && make

# 在仿真环境中测试
# 1. 进入 control_mode = 6 (RecoveryStand)
# 2. 观察控制台输出
# 3. 监控电机力矩值
```

### 2. 监控指标
- **身体高度**: 应稳定在 0.2-0.4m 范围
- **膝关节力矩**: 应 < 20 Nm
- **状态转换**: 应在站立完成后停止循环
- **关节位置**: 应收敛到目标位置并保持稳定

### 3. 调试输出
观察以下输出模式：
```
[RecoveryStand] Flag: FoldLegs, Iter: 0, Motion Iter: 0, Body Height: 0.xxx
[RecoveryStand] Flag: StandUp, Iter: 600, Motion Iter: 0, Body Height: 0.xxx  
[Recovery Balance] Standing motion complete, maintaining position
[RecoveryStand] Flag: StandUp, Iter: 1000, Motion Iter: 400, Body Height: 0.xxx
```

## 文件修改列表

1. `FSM_State_RecoveryStand.cpp`:
   - 调整站立目标位置
   - 修复状态机重启逻辑
   - 添加专用PD控制方法
   - 增强调试输出

2. `FSM_State_RecoveryStand.h`:
   - 声明新的 `_recoveryJointPDControl` 方法

## 注意事项

1. **渐进测试**: 先在仿真环境测试，确认无误后再在实际机器人上测试
2. **参数调优**: 如果力矩仍然偏高，可以进一步降低 PD 增益
3. **监控温度**: 注意电机温度，避免过热
4. **备份方案**: 保留原始参数设置，以便快速回滚
