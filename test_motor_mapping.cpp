#include <iostream>
#include <array>

int main() {
    std::cout << "=== Motor Mapping Test ===" << std::endl;
    
    // Test the leg to motor mapping
    const int leg_to_motor_mapping[4] = {1, 0, 3, 2}; // FR->1, FL->0, HR->3, HL->2
    
    const char* spi_leg_names[] = {"FR(0)", "FL(1)", "HR(2)", "HL(3)"};
    const char* motor_order_names[] = {"FL", "FR", "RL", "RR"};
    
    std::cout << "SPI Command Order -> Motor Command Order Mapping:" << std::endl;
    std::cout << "------------------------------------------------" << std::endl;
    
    for(int leg = 0; leg < 4; leg++) {
        int motor_idx = leg_to_motor_mapping[leg];
        std::cout << "SPI Leg " << spi_leg_names[leg] << " -> Motor " << motor_order_names[motor_idx] 
                  << " (index " << motor_idx << ")" << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "Joint Index Mapping:" << std::endl;
    std::cout << "-------------------" << std::endl;
    
    for(int leg = 0; leg < 4; leg++) {
        int motor_idx = leg_to_motor_mapping[leg];
        std::cout << "SPI Leg " << spi_leg_names[leg] << ":" << std::endl;
        std::cout << "  Abad: spi[" << leg*3+0 << "] -> motor[" << motor_idx*3+0 << "]" << std::endl;
        std::cout << "  Hip:  spi[" << leg*3+1 << "] -> motor[" << motor_idx*3+1 << "]" << std::endl;
        std::cout << "  Knee: spi[" << leg*3+2 << "] -> motor[" << motor_idx*3+2 << "]" << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "Recovery Stand Target Positions:" << std::endl;
    std::cout << "--------------------------------" << std::endl;
    
    // Test the new recovery stand positions
    float stand_jpos[4][3] = {
        {0.00571868f, 0.608813f, -1.21763f},   // FR: Front Right
        {-0.00571868f, 0.608813f, -1.21763f},  // FL: Front Left  
        {0.00571868f, 0.608813f, -1.21763f},   // HR: Hind Right
        {-0.00571868f, 0.608813f, -1.21763f}   // HL: Hind Left
    };
    
    for(int leg = 0; leg < 4; leg++) {
        std::cout << "Leg " << spi_leg_names[leg] << ": [" 
                  << stand_jpos[leg][0] << ", " 
                  << stand_jpos[leg][1] << ", " 
                  << stand_jpos[leg][2] << "]" << std::endl;
    }
    
    return 0;
}
