#!/usr/bin/env python3
"""
RecoveryStand 状态监控脚本
用于分析机器人在恢复站立过程中的力矩和姿态变化
"""

import re
import sys
from collections import defaultdict
import matplotlib.pyplot as plt
import numpy as np

class RecoveryStandMonitor:
    def __init__(self):
        self.torque_data = defaultdict(list)
        self.position_data = defaultdict(list)
        self.velocity_data = defaultdict(list)
        self.timestamps = []
        self.state_changes = []
        
    def parse_log_line(self, line):
        """解析日志行，提取电机状态信息"""
        
        # 解析状态变化
        if "[RecoveryStand]" in line:
            match = re.search(r'Flag: (\w+), Iter: (\d+), Motion Iter: (\d+), Body Height: ([\d.-]+), Max Torque: ([\d.-]+)', line)
            if match:
                flag, iter_num, motion_iter, height, max_torque = match.groups()
                self.state_changes.append({
                    'flag': flag,
                    'iter': int(iter_num),
                    'motion_iter': int(motion_iter),
                    'height': float(height),
                    'max_torque': float(max_torque)
                })
        
        # 解析腿部状态
        leg_match = re.search(r'Leg (\w+) \(ID:(\d+)\):', line)
        if leg_match:
            leg_name, leg_id = leg_match.groups()
            leg_id = int(leg_id)
            return leg_name, leg_id
            
        # 解析位置数据
        pos_match = re.search(r'Position: \[\s*([-\d.]+),\s*([-\d.]+),\s*([-\d.]+)\] rad', line)
        if pos_match:
            return 'position', [float(x) for x in pos_match.groups()]
            
        # 解析速度数据
        vel_match = re.search(r'Velocity: \[\s*([-\d.]+),\s*([-\d.]+),\s*([-\d.]+)\] rad/s', line)
        if vel_match:
            return 'velocity', [float(x) for x in vel_match.groups()]
            
        # 解析力矩数据
        torque_match = re.search(r'Torque:\s+\[\s*([-\d.]+),\s*([-\d.]+),\s*([-\d.]+)\] Nm', line)
        if torque_match:
            return 'torque', [float(x) for x in torque_match.groups()]
            
        return None, None
    
    def analyze_log_file(self, filename):
        """分析日志文件"""
        current_leg = None
        
        with open(filename, 'r') as f:
            for line_num, line in enumerate(f):
                result, data = self.parse_log_line(line.strip())
                
                if isinstance(result, str) and result.startswith('Leg'):
                    current_leg = data  # leg_id
                elif result == 'torque' and current_leg is not None:
                    self.torque_data[current_leg].append(data)
                elif result == 'position' and current_leg is not None:
                    self.position_data[current_leg].append(data)
                elif result == 'velocity' and current_leg is not None:
                    self.velocity_data[current_leg].append(data)
    
    def analyze_torque_patterns(self):
        """分析力矩模式"""
        print("=== 力矩分析报告 ===")
        
        leg_names = ['FR', 'FL', 'HR', 'HL']
        joint_names = ['Abad', 'Hip', 'Knee']
        
        for leg_id in range(4):
            if leg_id in self.torque_data:
                torques = np.array(self.torque_data[leg_id])
                print(f"\nLeg {leg_names[leg_id]} (ID:{leg_id}):")
                
                for joint in range(3):
                    joint_torques = torques[:, joint]
                    max_torque = np.max(np.abs(joint_torques))
                    mean_torque = np.mean(np.abs(joint_torques))
                    std_torque = np.std(joint_torques)
                    
                    print(f"  {joint_names[joint]}: Max={max_torque:.2f} Nm, Mean={mean_torque:.2f} Nm, Std={std_torque:.2f} Nm")
                    
                    if max_torque > 25.0:
                        print(f"    ⚠️  WARNING: {joint_names[joint]} 力矩过高!")
                    if std_torque > 10.0:
                        print(f"    ⚠️  WARNING: {joint_names[joint]} 力矩波动过大!")
    
    def plot_torque_trends(self):
        """绘制力矩趋势图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        leg_names = ['FR', 'FL', 'HR', 'HL']
        joint_names = ['Abad', 'Hip', 'Knee']
        colors = ['red', 'green', 'blue']
        
        for leg_id in range(4):
            if leg_id in self.torque_data:
                torques = np.array(self.torque_data[leg_id])
                
                for joint in range(3):
                    axes[leg_id].plot(torques[:, joint], 
                                    color=colors[joint], 
                                    label=joint_names[joint],
                                    alpha=0.7)
                
                axes[leg_id].set_title(f'Leg {leg_names[leg_id]} Torques')
                axes[leg_id].set_ylabel('Torque (Nm)')
                axes[leg_id].set_xlabel('Sample')
                axes[leg_id].legend()
                axes[leg_id].grid(True, alpha=0.3)
                
                # 添加危险线
                axes[leg_id].axhline(y=25, color='red', linestyle='--', alpha=0.5, label='Danger Level')
                axes[leg_id].axhline(y=-25, color='red', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig('recovery_stand_torques.png', dpi=300, bbox_inches='tight')
        print("力矩趋势图已保存为 'recovery_stand_torques.png'")
    
    def generate_recommendations(self):
        """生成优化建议"""
        print("\n=== 优化建议 ===")
        
        # 分析最大力矩
        max_torques = []
        for leg_id in range(4):
            if leg_id in self.torque_data:
                torques = np.array(self.torque_data[leg_id])
                max_torques.extend(np.max(np.abs(torques), axis=0))
        
        if max_torques:
            overall_max = np.max(max_torques)
            
            if overall_max > 30:
                print("🔴 严重: 力矩过高 (>30 Nm)")
                print("   建议: 进一步降低PD增益，特别是比例增益")
                print("   建议: 检查目标位置是否合理")
            elif overall_max > 20:
                print("🟡 警告: 力矩偏高 (>20 Nm)")
                print("   建议: 适当降低PD增益")
                print("   建议: 增加动作时间")
            else:
                print("🟢 良好: 力矩在合理范围内")
        
        # 分析状态变化
        if self.state_changes:
            flags = [change['flag'] for change in self.state_changes]
            if flags.count('StandUp') > flags.count('FoldLegs') * 2:
                print("🔴 检测到频繁的状态重启")
                print("   建议: 检查身体高度检测逻辑")
                print("   建议: 增加状态稳定时间")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 recovery_stand_monitor.py <log_file>")
        print("示例: python3 recovery_stand_monitor.py robot_log.txt")
        return
    
    monitor = RecoveryStandMonitor()
    
    try:
        monitor.analyze_log_file(sys.argv[1])
        monitor.analyze_torque_patterns()
        monitor.plot_torque_trends()
        monitor.generate_recommendations()
    except FileNotFoundError:
        print(f"错误: 找不到文件 {sys.argv[1]}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
