#ifndef QDLDL_TYPES_H
# define QDLDL_TYPES_H

# ifdef __cplusplus
extern "C" {
# endif /* ifdef __cplusplus */

// QDLDL integer and float types

typedef @QDLDL_INT_TYPE@    QDLDL_int;   /* for indices */
typedef @QDLDL_FLOAT_TYPE@  QDLDL_float; /* for numerical values  */
typedef @QDLDL_BOOL_TYPE@   QDLDL_bool;  /* for boolean values  */

# ifdef __cplusplus
}
# endif /* ifdef __cplusplus */

#endif /* ifndef QDLDL_TYPES_H */
