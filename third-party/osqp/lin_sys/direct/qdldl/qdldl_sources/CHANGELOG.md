Version 0.1.3 (11 September 2018)
----------------------------------
*   Julia implementation supports logical factorisation.
*   Changed `QDLDL_bool` to be unsigned char everywhere, except for the Julia examples where it is now treated as `Uint8`.


Version 0.1.2 (23 July 2018)
-----------------------------
*   Various cmake improvements.
*   Added pure Julia implementation.


Version 0.1.1 (19 July 2018)
-----------------------------
*   Fixed behaviour when data in A does not appear
    sequentially within each column.
*   Additional unit tests for non-sequential columns.
*   Types can be defined through cmake.


Version 0.1.0 (16 July 2018)
-----------------------------
*   Initial release
