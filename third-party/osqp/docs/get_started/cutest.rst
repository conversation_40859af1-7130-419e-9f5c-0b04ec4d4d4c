CUTEst
=======

To be able to use OSQP and `CUTEst <https://github.com/ralna/CUTEst/wiki>`_ you need to

* Install `CUTEst <https://github.com/ralna/CUTEst/wiki>`_
* Compile :ref:`OSQP from sources <build_from_sources>`
* Set the environment variable :code:`OSQP` to the main OSQP directory containing source code for which the compiled binary libraries lie in :code:`$OSQP/build/out`.

For more details, see the `README.osqp <https://github.com/ralna/CUTEst/blob/master/src/osqp/README.osqp>`_ file in the CUTEst repository.


