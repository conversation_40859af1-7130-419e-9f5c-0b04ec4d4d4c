Python
======

Before generating code for a parametric problem, the problem should be first
specified in the setup phase. See :ref:`python_setup` for more details.


Codegen
-------
The code is generated by running

.. code:: python

    m.codegen(dir_name, **opts)

The argument :code:`dir_name` is the name of a directory where the generated
code is stored.
Additional codegen options are shown in the following table

+-------------------------+-------------------------------------+--------------------------------+
| Option                  | Description                         | Allowed values                 |
+=========================+=====================================+================================+
| :code:`project_type`    | Build environment                   | | :code:`''` (default)         |
|                         |                                     | | :code:`'Makefile'`           |
|                         |                                     | | :code:`'MinGW Makefiles'`    |
|                         |                                     | | :code:`'Unix Makefiles'`     |
|                         |                                     | | :code:`'CodeBlocks'`         |
|                         |                                     | | :code:`'Xcode'`              |
+-------------------------+-------------------------------------+--------------------------------+
| :code:`parameters`      | Problem parameters                  | | :code:`'vectors'` (default)  |
|                         |                                     | | :code:`'matrices'`           |
+-------------------------+-------------------------------------+--------------------------------+
| :code:`python_ext_name` | Name of the generated Python module | | :code:`'emosqp'` (default)   |
|                         |                                     | | Nonempty string              |
+-------------------------+-------------------------------------+--------------------------------+
| :code:`force_rewrite`   | Rewrite existing directory          | | :code:`False` (default)      |
|                         |                                     | | :code:`True`                 |
+-------------------------+-------------------------------------+--------------------------------+

The options are passed using named arguments, e.g.,

.. code:: python

    m.codegen('code', parameters='matrices', python_ext_name='emosqp')

If the :code:`project_type` argument is not passed or is set to :code:`''`,
then no build files are generated.



Extension module API
--------------------
Once the code is generated, you can import a light python wrapper with

.. code:: python

    import emosqp

where :code:`emosqp` is the extension name given in the previous section. The module imports the following functions

.. py:function:: solve()
   :noindex:

   Solve the problem.

   :returns: tuple (x, y, status_val, iter, run_time)

             - **x** (*ndarray*) - Primal solution
             - **y** (*ndarray*) - Dual solution
             - **status_val** (*int*) - Status value as in :ref:`status_values`
             - **iter** (*int*) - Number of iterations
             - **run_time** (*double*) - Run time





.. py:function:: update_lin_cost(q_new)
   :noindex:

   Update linear cost.

   :param ndarray q_new: New linear cost vector


.. py:function:: update_lower_bound(l_new)
   :noindex:

   Update lower bound in the constraints.

   :param ndarray l_new: New lower bound vector


.. py:function:: update_upper_bound(u_new)
   :noindex:

   Update upper bound in the constraints.

   :param ndarray u_new: New upper bound vector


.. py:function:: update_bounds(l_new, u_new)
   :noindex:

   Update lower and upper bounds in the constraints.

   :param ndarray l_new: New lower bound vector
   :param ndarray u_new: New upper bound vector


If the code is generated with the option :code:`parameters` set to
:code:`'matrices'`, the following functions are also provided


.. py:function:: update_P(Px, Px_idx, Px_n)
  :noindex:

  Update nonzero entries of the quadratic cost matrix (only upper triangular) without changing sparsity structure.

  :param ndarray Px: Values of entries to be updated
  :param ndarray Px_idx: Indices of entries to be updated. Pass :code:`None` if
                         all the indices are to be updated
  :param int Px_n: Number of entries to be updated. Used only if Px_idx is not
                   :code:`None`.


.. py:function:: update_A(Ax, Ax_idx, Ax_n)
  :noindex:
  
  Update nonzero entries of the constraint matrix.

  :param ndarray Ax: Values of entries to be updated
  :param ndarray Ax_idx: Indices of entries to be updated. Pass :code:`None` if
                         all the indices are to be updated
  :param int Ax_n: Number of entries to be updated. Used only if Ax_idx is not
                   :code:`None`.


.. py:function:: update_P_A(Px, Px_idx, Px_n, Ax, Ax_idx, Ax_n)
  :noindex:

  Update nonzero entries of the quadratic cost and constraint matrices. It considers only the upper-triangular part of P.

  :param ndarray Px: Values of entries to be updated
  :param ndarray Px_idx: Indices of entries to be updated. Pass :code:`None` if
                         all the indices are to be updated
  :param int Px_n: Number of entries to be updated. Used only if Px_idx is not
                   :code:`None`.
  :param ndarray Ax: Values of entries to be updated
  :param ndarray Ax_idx: Indices of entries to be updated. Pass :code:`None` if
                         all the indices are to be updated
  :param int Ax_n: Number of entries to be updated. Used only if Ax_idx is not
                   :code:`None`.


You can update all the nonzero entries in matrix :math:`A` by running

.. code:: python

    emosqp.update_A(Ax_new, None, 0);

See C/C++ :ref:`C_sublevel_API` for more details on the input arguments.
