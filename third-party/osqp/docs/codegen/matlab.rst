Matlab
======

Before generating code for a parametric problem, the problem should be first
specified in the setup phase. See :ref:`matlab_setup` for more details.


Codegen
-------
The code is generated by running

.. code:: matlab

    m.codegen(dir_name, varargin)

The argument :code:`dir_name` is the name of a directory where the generated
code is stored.
The second argument :code:`varargin` specifies additional codegen options
shown in the following table


+-----------------------+-------------------------------------+--------------------------------+
| Option                | Description                         | Allowed values                 |
+=======================+=====================================+================================+
| :code:`project_type`  | Build environment                   | | :code:`''` (default)         |
|                       |                                     | | :code:`'Makefile'`           |
|                       |                                     | | :code:`'MinGW Makefiles'`    |
|                       |                                     | | :code:`'Unix Makefiles'`     |
|                       |                                     | | :code:`'CodeBlocks'`         |
|                       |                                     | | :code:`'Xcode'`              |
+-----------------------+-------------------------------------+--------------------------------+
| :code:`parameters`    | Problem parameters                  | | :code:`'vectors'` (default)  |
|                       |                                     | | :code:`'matrices'`           |
+-----------------------+-------------------------------------+--------------------------------+
| :code:`mexname`       | Name of the compiled mex interface  | | :code:`'emosqp'` (default)   |
|                       |                                     | | Nonempty string              |
+-----------------------+-------------------------------------+--------------------------------+
| :code:`force_rewrite` | Rewrite existing directory          | | :code:`false` (default)      |
|                       |                                     | | :code:`true`                 |
+-----------------------+-------------------------------------+--------------------------------+

You can pass the options as field-value pairs, e.g.,

.. code:: matlab

    m.codegen('code', 'parameters', 'matrices', 'mexname', 'emosqp');

If the :code:`project_type` argument is not passed or is set to :code:`''`,
then no build files are generated.



Mex interface
-------------
Once the code is generated the following functions are provided through its mex interface. Each function is called as

.. code:: matlab

    emosqp('function_name');


where :code:`emosqp` is the name of the mex interface specified in the previous section

.. function:: emosqp('solve')
   :noindex:

   Solve the problem.

   :returns: multiple variables [x, y, status_val, iter, run_time]

             - **x** (*ndarray*) - Primal solution
             - **y** (*ndarray*) - Dual solution
             - **status_val** (*int*) - Status value as in :ref:`status_values`
             - **iter** (*int*) - Number of iterations
             - **run_time** (*double*) - Run time


.. function:: emosqp('update_lin_cost', q_new)
   :noindex:

   Update linear cost.

   :param ndarray q_new: New linear cost vector


.. function:: emosqp('update_lower_bound', l_new)
   :noindex:

   Update lower bound in the constraints.

   :param ndarray l_new: New lower bound vector


.. function:: emosqp('update_upper_bound', u_new)
   :noindex:

   Update upper bound in the constraints.

   :param ndarray u_new: New upper bound vector


.. function:: emosqp('update_bounds', l_new, u_new)
   :noindex:

   Update lower and upper bounds in the constraints.

   :param ndarray l_new: New lower bound vector
   :param ndarray u_new: New upper bound vector


If the code is generated with the option :code:`parameters` set to
:code:`'matrices'`, then the following functions are also provided


.. function:: emosqp('update_P', Px, Px_idx, Px_n)
   :noindex:

  Update nonzero entries of the quadratic cost matrix (only upper triangular) without changing sparsity structure.

   :param ndarray Px: Values of entries to be updated
   :param ndarray Px_idx: Indices of entries to be updated. Pass :code:`[]` if
                         all the indices are to be updated
   :param int Px_n: Number of entries to be updated. Used only if Px_idx is not
                   :code:`[]`.


.. function:: emosqp('update_A', Ax, Ax_idx, Ax_n)
   :noindex:

   Update nonzero entries of the constraint matrix.

   :param ndarray Ax: Values of entries to be updated
   :param ndarray Ax_idx: Indices of entries to be updated. Pass :code:`[]` if
                         all the indices are to be updated
   :param int Ax_n: Number of entries to be updated. Used only if Ax_idx is not
                   :code:`[]`.


.. function:: emosqp('update_P_A', Px, Px_idx, Px_n, Ax, Ax_idx, Ax_n)
   :noindex:

  Update nonzero entries of the quadratic cost and constraint matrices. It considers only the upper-triangular part of P.

   :param ndarray Px: Values of entries to be updated
   :param ndarray Px_idx: Indices of entries to be updated. Pass :code:`[]` if
                         all the indices are to be updated
   :param int Px_n: Number of entries to be updated. Used only if Px_idx is not
                   :code:`[]`.
   :param ndarray Ax: Values of entries to be updated
   :param ndarray Ax_idx: Indices of entries to be updated. Pass :code:`[]` if
                         all the indices are to be updated
   :param int Ax_n: Number of entries to be updated. Used only if Ax_idx is not
                   :code:`[]`.


You can update all the nonzero entries in matrix :math:`A` by running

.. code:: matlab

    emosqp('update_A', Ax_new, [], 0);

See C/C++ :ref:`C_sublevel_API` for more details on the input arguments.
