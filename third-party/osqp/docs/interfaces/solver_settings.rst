.. _solver_settings :

Solver settings
---------------

The solver settings are displayed in the following table. The settings marked with * can be changed without running the setup method again.

.. tabularcolumns:: |p{4.5cm}|p{3.5cm}|p{6.5cm}|L|

+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| Argument                       | Description                                                 | Allowed values                                               | Default value   |
+================================+=============================================================+==============================================================+=================+
| :code:`rho` *                  | ADMM rho step                                               | 0 < :code:`rho`                                              | 0.1             |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`sigma`                  | ADMM sigma step                                             | 0 < :code:`sigma`                                            | 1e-06           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`max_iter` *             | Maximum number of iterations                                | 0 < :code:`max_iter` (integer)                               | 4000            |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`eps_abs` *              | Absolute tolerance                                          | 0 <= :code:`eps_abs`                                         | 1e-03           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`eps_rel` *              | Relative tolerance                                          | 0 <= :code:`eps_rel`                                         | 1e-03           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`eps_prim_inf` *         | Primal infeasibility tolerance                              | 0 <= :code:`eps_prim_inf`                                    | 1e-04           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`eps_dual_inf` *         | Dual infeasibility tolerance                                | 0 <= :code:`eps_dual_inf`                                    | 1e-04           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`alpha` *                | ADMM overrelaxation parameter                               | 0 < :code:`alpha` < 2                                        | 1.6             |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`linsys_solver`          | Linear systems solver type                                  | See :ref:`linear_system_solvers_setting`                     | qdldl           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`delta` *                | Polishing regularization parameter                          | 0 < :code:`delta`                                            | 1e-06           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`polish` *               | Perform polishing                                           | True/False                                                   | False           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`polish_refine_iter` *   | Refinement iterations in polish                             | 0 < :code:`polish_refine_iter` (integer)                     | 3               |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`verbose` *              | Print output                                                | True/False                                                   | True            |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`scaled_termination` *   | Scaled termination conditions                               | True/False                                                   | False           |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`check_termination` *    | Check termination interval                                  | 0 (disabled) or 0 < :code:`check_termination` (integer)      | 25              |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`warm_start` *           | Perform warm starting                                       | True/False                                                   | True            |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`scaling`                | Number of scaling iterations                                | 0 (disabled) or 0 < :code:`scaling` (integer)                | 10              |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`adaptive_rho`           | Adaptive rho                                                | True/False                                                   | True            |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`adaptive_rho_interval`  | Adaptive rho interval                                       | 0 (automatic) or 0 < :code:`adaptive_rho_interval` (integer) | 0               |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`adaptive_rho_tolerance` | Tolerance for adapting rho                                  | 1 <= :code:`adaptive_rho_tolerance`                          | 5               |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`adaptive_rho_fraction`  | Adaptive rho interval as fraction of setup time (auto mode) | 0 < :code:`adaptive_rho_fraction`                            | 0.4             |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+
| :code:`time_limit` *           | Run time limit in seconds                                   | 0 (disabled) or 0 <= :code:`time_limit`                      | 0               |
+--------------------------------+-------------------------------------------------------------+--------------------------------------------------------------+-----------------+

The boolean values :code:`True/False` are defined as :code:`1/0` in the C/C++ interfaces.


.. The infinity values correspond to:
..
.. +----------+--------------------+
.. | Language | Value              |
.. +==========+====================+
.. | C        | :code:`OSQP_INFTY` |
.. +----------+--------------------+
.. | Python   | :code:`numpy.inf`  |
.. +----------+--------------------+
.. | Matlab   | :code:`Inf`        |
.. +----------+--------------------+
.. | Julia    | :code:`Inf`        |
.. +----------+--------------------+
