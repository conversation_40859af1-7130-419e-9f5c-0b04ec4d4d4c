# C language
# -------------------------------------------------------------------
# Compile commands for cquery
compile_commands.json

# Out folder
out/

# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Code coverage
*.gcda
*.gcno
coverage.info
coverage_html/

# Doxygen output
docs/doxygen_out

# Editor files
# -------------------------------------------------------------------
*.swp
.ycm_extra_conf.py
*.swo
*.vscode/



# Gurobi
# -------------------------------------------------------------------
gurobi.log


# Tags
# -------------------------------------------------------------------
tags
.tags
.tags1
TAGS

# Python Language
# -------------------------------------------------------------------
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# IPython Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# dotenv
.env

# virtualenv
.venv/
venv/
ENV/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

# Python 3 porting backup files
*.bak

# Mac OSX Files
# -------------------------------------------------------------------
.DS_Store


# Matlab
# -------------------------------------------------------------------
# Files generated during compilation
*.o
*.m~
*.mexmaci64*
*.mexw64*
*.mexa64*
out/
*.asv

# Other data files
# *.mat

# Emacs
\#*\#
.\#*
*~

# Clang completer
# -------------------------------------------------------------------
.clang_complete

# CMake Files
# -------------------------------------------------------------------
*.stamp

