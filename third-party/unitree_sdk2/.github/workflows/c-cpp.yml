name: C/C++ CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ubuntu-20.04

    steps:
    - uses: actions/checkout@v4
    - name: install deps
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake g++ build-essential libyaml-cpp-dev
    - name: cmake
      run: |
        cmake -Bbuild
        cmake --build build -j
