/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: SportModeCmd_.idl
  Source: SportModeCmd_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_SPORTMODECMD__HPP
#define DDSCXX_UNITREE_IDL_GO2_SPORTMODECMD__HPP

#include "unitree/idl/go2/BmsCmd_.hpp"

#include "unitree/idl/go2/PathPoint_.hpp"

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class SportModeCmd_
{
private:
 uint8_t mode_ = 0;
 uint8_t gait_type_ = 0;
 uint8_t speed_level_ = 0;
 float foot_raise_height_ = 0.0f;
 float body_height_ = 0.0f;
 std::array<float, 2> position_ = { };
 std::array<float, 3> euler_ = { };
 std::array<float, 2> velocity_ = { };
 float yaw_speed_ = 0.0f;
 ::unitree_go::msg::dds_::BmsCmd_ bms_cmd_;
 std::array<::unitree_go::msg::dds_::PathPoint_, 30> path_point_ = { };

public:
  SportModeCmd_() = default;

  explicit SportModeCmd_(
    uint8_t mode,
    uint8_t gait_type,
    uint8_t speed_level,
    float foot_raise_height,
    float body_height,
    const std::array<float, 2>& position,
    const std::array<float, 3>& euler,
    const std::array<float, 2>& velocity,
    float yaw_speed,
    const ::unitree_go::msg::dds_::BmsCmd_& bms_cmd,
    const std::array<::unitree_go::msg::dds_::PathPoint_, 30>& path_point) :
    mode_(mode),
    gait_type_(gait_type),
    speed_level_(speed_level),
    foot_raise_height_(foot_raise_height),
    body_height_(body_height),
    position_(position),
    euler_(euler),
    velocity_(velocity),
    yaw_speed_(yaw_speed),
    bms_cmd_(bms_cmd),
    path_point_(path_point) { }

  uint8_t mode() const { return this->mode_; }
  uint8_t& mode() { return this->mode_; }
  void mode(uint8_t _val_) { this->mode_ = _val_; }
  uint8_t gait_type() const { return this->gait_type_; }
  uint8_t& gait_type() { return this->gait_type_; }
  void gait_type(uint8_t _val_) { this->gait_type_ = _val_; }
  uint8_t speed_level() const { return this->speed_level_; }
  uint8_t& speed_level() { return this->speed_level_; }
  void speed_level(uint8_t _val_) { this->speed_level_ = _val_; }
  float foot_raise_height() const { return this->foot_raise_height_; }
  float& foot_raise_height() { return this->foot_raise_height_; }
  void foot_raise_height(float _val_) { this->foot_raise_height_ = _val_; }
  float body_height() const { return this->body_height_; }
  float& body_height() { return this->body_height_; }
  void body_height(float _val_) { this->body_height_ = _val_; }
  const std::array<float, 2>& position() const { return this->position_; }
  std::array<float, 2>& position() { return this->position_; }
  void position(const std::array<float, 2>& _val_) { this->position_ = _val_; }
  void position(std::array<float, 2>&& _val_) { this->position_ = _val_; }
  const std::array<float, 3>& euler() const { return this->euler_; }
  std::array<float, 3>& euler() { return this->euler_; }
  void euler(const std::array<float, 3>& _val_) { this->euler_ = _val_; }
  void euler(std::array<float, 3>&& _val_) { this->euler_ = _val_; }
  const std::array<float, 2>& velocity() const { return this->velocity_; }
  std::array<float, 2>& velocity() { return this->velocity_; }
  void velocity(const std::array<float, 2>& _val_) { this->velocity_ = _val_; }
  void velocity(std::array<float, 2>&& _val_) { this->velocity_ = _val_; }
  float yaw_speed() const { return this->yaw_speed_; }
  float& yaw_speed() { return this->yaw_speed_; }
  void yaw_speed(float _val_) { this->yaw_speed_ = _val_; }
  const ::unitree_go::msg::dds_::BmsCmd_& bms_cmd() const { return this->bms_cmd_; }
  ::unitree_go::msg::dds_::BmsCmd_& bms_cmd() { return this->bms_cmd_; }
  void bms_cmd(const ::unitree_go::msg::dds_::BmsCmd_& _val_) { this->bms_cmd_ = _val_; }
  void bms_cmd(::unitree_go::msg::dds_::BmsCmd_&& _val_) { this->bms_cmd_ = _val_; }
  const std::array<::unitree_go::msg::dds_::PathPoint_, 30>& path_point() const { return this->path_point_; }
  std::array<::unitree_go::msg::dds_::PathPoint_, 30>& path_point() { return this->path_point_; }
  void path_point(const std::array<::unitree_go::msg::dds_::PathPoint_, 30>& _val_) { this->path_point_ = _val_; }
  void path_point(std::array<::unitree_go::msg::dds_::PathPoint_, 30>&& _val_) { this->path_point_ = _val_; }

  bool operator==(const SportModeCmd_& _other) const
  {
    (void) _other;
    return mode_ == _other.mode_ &&
      gait_type_ == _other.gait_type_ &&
      speed_level_ == _other.speed_level_ &&
      foot_raise_height_ == _other.foot_raise_height_ &&
      body_height_ == _other.body_height_ &&
      position_ == _other.position_ &&
      euler_ == _other.euler_ &&
      velocity_ == _other.velocity_ &&
      yaw_speed_ == _other.yaw_speed_ &&
      bms_cmd_ == _other.bms_cmd_ &&
      path_point_ == _other.path_point_;
  }

  bool operator!=(const SportModeCmd_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::getTypeName()
{
  return "unitree_go::msg::dds_::SportModeCmd_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::type_map_blob_sz() { return 1506; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::type_info_blob_sz() { return 196; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x13,  0x02,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf1,  0x4b,  0xbc,  0x00,  0xba,  0x9a,  0x8c,  0x2d, 
 0x02,  0x68,  0x52,  0x1d,  0xd0,  0xd4,  0xb6,  0x00,  0x10,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xe2,  0xc7,  0x6d,  0x1e,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x3b,  0xbb,  0xc1,  0x77,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xb5,  0x46,  0x7f,  0xa0,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xe0,  0x34,  0x4c,  0x32,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x09,  0x47,  0x57,  0xfe,  0x07,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x09,  0xbc,  0x64,  0x6b,  0xe3,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x09,  0xac,  0x1a, 
 0x45,  0x3d,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x25, 
 0xcf,  0xab,  0x66,  0x00,  0x19,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x3e, 
 0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23,  0x11,  0x50,  0x97, 
 0xc7,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf1, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x1e,  0xf1,  0x6d,  0x6e,  0x83,  0x23,  0xe9,  0x1a, 
 0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2,  0xdc,  0x56,  0xb6,  0x0a,  0xf1,  0x3e,  0x8e,  0xb6, 
 0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23,  0x00,  0x3e,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2e,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x32, 
 0x62,  0xd4,  0x8d,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x02,  0x9c,  0x3b,  0x62,  0x94,  0xf1,  0x6d, 
 0x6e,  0x83,  0x23,  0xe9,  0x1a,  0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2,  0x00,  0x00,  0x00, 
 0x83,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x73,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0xe9,  0x6c,  0x41,  0xab,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x9d,  0xd4,  0xe4,  0x61,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x41,  0x52,  0x90,  0x76,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x92,  0xdd,  0xa6,  0x44,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x76,  0x7f,  0xc7,  0x3d,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x94,  0x95,  0xfa,  0x6c,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x83,  0x29,  0x00,  0xa3,  0x00,  0x63,  0x03,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0xf2,  0xbb,  0xbb,  0x27,  0xfd,  0xc5,  0x19,  0x66,  0x1a,  0x6a,  0xcc,  0x38,  0x22,  0x2e,  0x2f,  0x00, 
 0xc9,  0x01,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2d,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x53,  0x70,  0x6f,  0x72,  0x74, 
 0x4d,  0x6f,  0x64,  0x65,  0x43,  0x6d,  0x64,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x8d,  0x01,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x67,  0x61,  0x69,  0x74, 
 0x5f,  0x74,  0x79,  0x70,  0x65,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x73,  0x70,  0x65,  0x65,  0x64,  0x5f,  0x6c,  0x65, 
 0x76,  0x65,  0x6c,  0x00,  0x00,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x12,  0x00,  0x00,  0x00,  0x66,  0x6f,  0x6f,  0x74,  0x5f,  0x72,  0x61,  0x69, 
 0x73,  0x65,  0x5f,  0x68,  0x65,  0x69,  0x67,  0x68,  0x74,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x62,  0x6f,  0x64,  0x79, 
 0x5f,  0x68,  0x65,  0x69,  0x67,  0x68,  0x74,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x09,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x70,  0x6f,  0x73,  0x69,  0x74,  0x69,  0x6f,  0x6e, 
 0x00,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x65,  0x75,  0x6c,  0x65,  0x72,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x09,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x76,  0x65,  0x6c,  0x6f,  0x63,  0x69,  0x74,  0x79,  0x00,  0x00,  0x00,  0x00, 
 0x18,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x79,  0x61,  0x77,  0x5f,  0x73,  0x70,  0x65,  0x65,  0x64,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3, 
 0xf1,  0x6b,  0x34,  0x22,  0x33,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x62,  0x6d,  0x73,  0x5f, 
 0x63,  0x6d,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x31,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf2,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x1e,  0xf2,  0xd6,  0x62, 
 0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf,  0xa2,  0xd4,  0x15,  0x0b,  0x00,  0x00,  0x00, 
 0x70,  0x61,  0x74,  0x68,  0x5f,  0x70,  0x6f,  0x69,  0x6e,  0x74,  0x00,  0x00,  0x00,  0xf2,  0x31,  0xa8, 
 0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3,  0xf1,  0x6b,  0x34,  0x22,  0x33,  0x76,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x42,  0x6d,  0x73,  0x43,  0x6d,  0x64,  0x5f,  0x00,  0x00, 
 0x42,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x00,  0x04,  0x00,  0x00,  0x00,  0x6f,  0x66,  0x66,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x02,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65, 
 0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0xf2,  0xd6,  0x62,  0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae, 
 0x65,  0xcf,  0xa2,  0xd4,  0x15,  0x00,  0x00,  0x00,  0xe3,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x2a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73, 
 0x5f,  0x3a,  0x3a,  0x50,  0x61,  0x74,  0x68,  0x50,  0x6f,  0x69,  0x6e,  0x74,  0x5f,  0x00,  0x00,  0x00, 
 0xab,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x1b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x74,  0x5f,  0x66,  0x72,  0x6f,  0x6d,  0x5f,  0x73, 
 0x74,  0x61,  0x72,  0x74,  0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00,  0x78,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00,  0x79,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x79,  0x61,  0x77,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x76,  0x78,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x76,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x05,  0x00,  0x00,  0x00,  0x76,  0x79,  0x61,  0x77,  0x00,  0x00,  0x00,  0x00, 
 0x5e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf2,  0xbb,  0xbb,  0x27,  0xfd,  0xc5,  0x19,  0x66, 
 0x1a,  0x6a,  0xcc,  0x38,  0x22,  0x2e,  0x2f,  0xf1,  0x4b,  0xbc,  0x00,  0xba,  0x9a,  0x8c,  0x2d,  0x02, 
 0x68,  0x52,  0x1d,  0xd0,  0xd4,  0xb6,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3, 
 0xf1,  0x6b,  0x34,  0x22,  0x33,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf, 
 0x51,  0xed,  0x4a,  0x23,  0xf2,  0xd6,  0x62,  0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf, 
 0xa2,  0xd4,  0x15,  0xf1,  0x6d,  0x6e,  0x83,  0x23,  0xe9,  0x1a,  0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6, 
 0xbd,  0xe2, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xc0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x4b,  0xbc,  0x00,  0xba,  0x9a,  0x8c,  0x2d,  0x02,  0x68,  0x52,  0x1d, 
 0xd0,  0xd4,  0xb6,  0x00,  0x14,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0, 
 0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23,  0x00,  0x42,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x6d,  0x6e,  0x83,  0x23,  0xe9,  0x1a,  0xd4,  0x2a,  0x82,  0x33,  0xae,  0xf6,  0xbd,  0xe2,  0x00, 
 0x87,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0xbb,  0xbb,  0x27,  0xfd,  0xc5,  0x19,  0x66,  0x1a,  0x6a,  0xcc,  0x38, 
 0x22,  0x2e,  0x2f,  0x00,  0xcd,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a, 
 0xf3,  0xd3,  0xf1,  0x6b,  0x34,  0x22,  0x33,  0x00,  0x7a,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xd6,  0x62,  0x9a,  0x35,  0x54,  0x6d,  0x9f,  0xc9,  0xae,  0x65,  0xcf,  0xa2,  0xd4,  0x15,  0x00, 
 0xe7,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::SportModeCmd_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::SportModeCmd_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::SportModeCmd_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::SportModeCmd_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::SportModeCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.speed_level()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.euler()[0], instance.euler().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.path_point()) {  //array depth 1
      if (!write(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::SportModeCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeCmd_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::SportModeCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.speed_level()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.euler()[0], instance.euler().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (auto & a_1:instance.path_point()) {  //array depth 1
      if (!read(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::SportModeCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeCmd_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::SportModeCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.speed_level()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.euler()[0], instance.euler().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.path_point()) {  //array depth 1
      if (!move(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::SportModeCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeCmd_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::SportModeCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.gait_type()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.speed_level()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.foot_raise_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.body_height()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.position()[0], instance.position().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.euler()[0], instance.euler().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.velocity()[0], instance.velocity().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.yaw_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.path_point()) {  //array depth 1
      if (!max(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::SportModeCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::SportModeCmd_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_SPORTMODECMD__HPP
