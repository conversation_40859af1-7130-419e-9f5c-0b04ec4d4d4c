/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: BmsCmd_.idl
  Source: BmsCmd_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_BMSCMD__HPP
#define DDSCXX_UNITREE_IDL_GO2_BMSCMD__HPP

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class BmsCmd_
{
private:
 uint8_t off_ = 0;
 std::array<uint8_t, 3> reserve_ = { };

public:
  BmsCmd_() = default;

  explicit BmsCmd_(
    uint8_t off,
    const std::array<uint8_t, 3>& reserve) :
    off_(off),
    reserve_(reserve) { }

  uint8_t off() const { return this->off_; }
  uint8_t& off() { return this->off_; }
  void off(uint8_t _val_) { this->off_ = _val_; }
  const std::array<uint8_t, 3>& reserve() const { return this->reserve_; }
  std::array<uint8_t, 3>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint8_t, 3>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint8_t, 3>&& _val_) { this->reserve_ = _val_; }

  bool operator==(const BmsCmd_& _other) const
  {
    (void) _other;
    return off_ == _other.off_ &&
      reserve_ == _other.reserve_;
  }

  bool operator!=(const BmsCmd_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::getTypeName()
{
  return "unitree_go::msg::dds_::BmsCmd_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::type_map_blob_sz() { return 278; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x56,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0, 
 0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23,  0x00,  0x3e,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2e,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x32,  0x62,  0xd4,  0x8d,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x02,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0x8e,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3,  0xf1,  0x6b, 
 0x34,  0x22,  0x33,  0x00,  0x76,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x27,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x42, 
 0x6d,  0x73,  0x43,  0x6d,  0x64,  0x5f,  0x00,  0x00,  0x42,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x6f,  0x66,  0x66,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x02,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a, 
 0xf3,  0xd3,  0xf1,  0x6b,  0x34,  0x22,  0x33,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e, 
 0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf,  0x51, 
 0xed,  0x4a,  0x23,  0x00,  0x42,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3,  0xf1,  0x6b, 
 0x34,  0x22,  0x33,  0x00,  0x7a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::BmsCmd_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::BmsCmd_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::BmsCmd_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::BmsCmd_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::BmsCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.off()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::BmsCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsCmd_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::BmsCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.off()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::BmsCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsCmd_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::BmsCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.off()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::BmsCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsCmd_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::BmsCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.off()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::BmsCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::BmsCmd_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_BMSCMD__HPP
