# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lihy_ws/gsmp_v2-sdk

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lihy_ws/gsmp_v2-sdk

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/serial//CMakeFiles/progress.marks
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/serial/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/serial/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/serial/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/serial/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
third-party/serial/CMakeFiles/serial.dir/rule:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 third-party/serial/CMakeFiles/serial.dir/rule
.PHONY : third-party/serial/CMakeFiles/serial.dir/rule

# Convenience name for target.
serial: third-party/serial/CMakeFiles/serial.dir/rule
.PHONY : serial

# fast build rule for target.
serial/fast:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/build
.PHONY : serial/fast

src/impl/list_ports/list_ports_linux.o: src/impl/list_ports/list_ports_linux.cc.o
.PHONY : src/impl/list_ports/list_ports_linux.o

# target to build an object file
src/impl/list_ports/list_ports_linux.cc.o:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/impl/list_ports/list_ports_linux.cc.o
.PHONY : src/impl/list_ports/list_ports_linux.cc.o

src/impl/list_ports/list_ports_linux.i: src/impl/list_ports/list_ports_linux.cc.i
.PHONY : src/impl/list_ports/list_ports_linux.i

# target to preprocess a source file
src/impl/list_ports/list_ports_linux.cc.i:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/impl/list_ports/list_ports_linux.cc.i
.PHONY : src/impl/list_ports/list_ports_linux.cc.i

src/impl/list_ports/list_ports_linux.s: src/impl/list_ports/list_ports_linux.cc.s
.PHONY : src/impl/list_ports/list_ports_linux.s

# target to generate assembly for a file
src/impl/list_ports/list_ports_linux.cc.s:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/impl/list_ports/list_ports_linux.cc.s
.PHONY : src/impl/list_ports/list_ports_linux.cc.s

src/impl/unix.o: src/impl/unix.cc.o
.PHONY : src/impl/unix.o

# target to build an object file
src/impl/unix.cc.o:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/impl/unix.cc.o
.PHONY : src/impl/unix.cc.o

src/impl/unix.i: src/impl/unix.cc.i
.PHONY : src/impl/unix.i

# target to preprocess a source file
src/impl/unix.cc.i:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/impl/unix.cc.i
.PHONY : src/impl/unix.cc.i

src/impl/unix.s: src/impl/unix.cc.s
.PHONY : src/impl/unix.s

# target to generate assembly for a file
src/impl/unix.cc.s:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/impl/unix.cc.s
.PHONY : src/impl/unix.cc.s

src/serial.o: src/serial.cc.o
.PHONY : src/serial.o

# target to build an object file
src/serial.cc.o:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/serial.cc.o
.PHONY : src/serial.cc.o

src/serial.i: src/serial.cc.i
.PHONY : src/serial.i

# target to preprocess a source file
src/serial.cc.i:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/serial.cc.i
.PHONY : src/serial.cc.i

src/serial.s: src/serial.cc.s
.PHONY : src/serial.s

# target to generate assembly for a file
src/serial.cc.s:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/src/serial.cc.s
.PHONY : src/serial.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... serial"
	@echo "... src/impl/list_ports/list_ports_linux.o"
	@echo "... src/impl/list_ports/list_ports_linux.i"
	@echo "... src/impl/list_ports/list_ports_linux.s"
	@echo "... src/impl/unix.o"
	@echo "... src/impl/unix.i"
	@echo "... src/impl/unix.s"
	@echo "... src/serial.o"
	@echo "... src/serial.i"
	@echo "... src/serial.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

