# @author: <PERSON><PERSON>
# @date: 2023-8-9
# @brief: update the SOEM to the latest version

add_subdirectory(soem)
add_subdirectory(serial)

target_compile_options(serial PUBLIC -fPIC) # 保证生成的库与位置无关
target_compile_options(soem PRIVATE -fPIC)  # 保证生成的库与位置无关

get_target_property(SOEM_INCLUDE_DIRS_LOCAL soem INTERFACE_INCLUDE_DIRECTORIES)
set(SOEM_INCLUDE_DIRS ${SOEM_INCLUDE_DIRS_LOCAL} CACHE INTERNAL "SOEM Include Path")

get_target_property(SERIAL_INCLUDE_DIRS_LOCAL serial INTERFACE_INCLUDE_DIRECTORIES)
set(SERIAL_INCLUDE_DIRS ${SERIAL_INCLUDE_DIRS_LOCAL} CACHE INTERNAL "serial Include Path")
