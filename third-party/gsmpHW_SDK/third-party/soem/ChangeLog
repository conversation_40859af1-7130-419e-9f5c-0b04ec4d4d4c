Version 1.1.2 : 2008-12-23
- First public release
Version 1.1.3 : 2009-01-18
- Added CoE, RxPDO and TxPDO (still Beta)
- Added FoE, Read and Write (still Beta)
- Fixed BigEndian conversion missing in ethercatconfig.c (credit:<PERSON>)
- Fixed segmented transfer bug in ethercatcoe.c
Version 1.1.4 : 2009-04-22
- Changed FMMU configuration algorithm
- Changed ec_slave structure around SM and FMMU storage
- Added SYNC1 configuration
- Fixed bug in FoE write
Version 1.2.0 : 2009-09-12
- Changed the license to GPLv2 only
- Added note to usage terms (please read carefully)
- Eeprom acces is released to slave after preop
- Fixed linux-64 define bug (uint32 and int32)
- Fixed maximum frame size
Version 1.2.2 : 2010-02-22
- Fixed bugs in ec_adddatagram.
- Fixed several bugs in CoE object dictionary read functions.
- Fixed bug in PDO mapping read function.
- Changed ec_slave structure around topology and delay variables.
- Added several constants in ethercattype.c
Version 1.2.3 : 2010-03-07
- Clear SM enable if size is 0, even if enable is set in SII.
- Fixed bug in DC propagation delay calculation. Branches with only non DC slaves now correctly close root port.
- Fixed bug in ec_receive_processdata(), wkc now checks for EC_NOFRAME instead of 0.
- Fixed bug in makefile
Version 1.2.4 : 2010-04-10
- Added SoE, servo over EtherCAT support.
- Added SoE read request and write request.
- Added SoE segmented transfers.
- Added SoE error response.
- Added SoE errors to print module.
- Added Auto config of SoE process data.
Version 1.2.5 : 2011-06-13
- Added eepromtool, it can read and write the ESC eeprom of a designated slave.
- Rewrite of eeprom read/write functions.
- Added infrastructure change to allow slave groups.
- Added recovery and reconfiguration of slaves after connection loss.
- Improved CoE PDO assignment read from slaves, no longer assume assign indexes as functionally fixed.
- Fixed bugs in ec_config_map().
- Added EC_STATE_BOOT constant.
- Fixed mailbox size bug, In and Out mailbox can now be of different size.
- Fixed SM type bug.
- Fixed FoE bugs.
- Fixed siigetbyte() unaligned copy.
- Fixed bug in nicdrv.c, socket handles are 0 included.
- Fixed bug in ethercatconfig.c causing memory corruption.
Version 1.2.8 : 2012-06-14
- Changed directory structure.
- Changed make file.
- Moved hardware / OS dependend part in separate directories.
- Added firm_update tool to upload firmware to slaves in Boot state, use with care.
- Added DC for LRD/LWR case.
- Separated expectedWKC to inputsWKC and outputsWKC.
- Added PreOP->SafeOP hooks in configuration functions.
- With CoE use expedited download if mailbox size is very small and object <= 4 bytes.
- Fixed NetX mailbox configuration behaviour.
- Fixed FoE write bug.
- Added mailbox error handling.
- Fixed SII string read bug.
- Fixed bug in table lookup for printing
- Rewrite of ec_recover_slave() and ec_reconfigure_slave()
- Added -map option in slaveinfo, shows SOEM IO mapping of all slaves found.
Version 1.3.0 : 2013-02-24
- Added win32 target.
- Added rtk target.
- Compiles under gcc / visual-c / borland-c.
- Multiple port support. One master can run concurrent stacks on multiple network ports.
- All global vars are encapsulated in context struct.
- All timing abstracted in osal.c.
- Linux timing converted to get_clock(CLOCK_MONOTONIC).
- Error messages updated to latest ETG1020 document.
- FoE transfers now support busy response.
- Fixed NetX100 configuration behaviour.
- Fixed linux gettimeofday() to get_clock().
- Fixed eeprom cache flush on reinit.
- Fixed make for new gcc linker version.
Version 1.3.1 : 2015-03-11
- Added intime target.
- Added rtk\fec target.
- Compiles under gcc / visual-c / borland-c / intime.
- Added multi-threaded configuration for parallel configurations of slaves
Version 1.3.2 : 2018-02-02
 - Made a mistake. DON'T USE!
Version 1.3.3 : 2018-02-02
- Added rtems target.
- Added support for overlapping IOmap.

