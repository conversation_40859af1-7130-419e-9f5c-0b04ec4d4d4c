
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/osal/linux/osal.c" "third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/nicdrv.c" "third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/oshw.c" "third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatbase.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatcoe.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatconfig.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatdc.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercateoe.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatfoe.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatmain.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatprint.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o.d"
  "/home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatsoe.c" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o" "gcc" "third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
