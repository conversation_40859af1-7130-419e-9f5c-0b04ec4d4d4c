/usr/bin/cc -fPIC -shared -Wl,-soname,libsoem.so -o libsoem.so CMakeFiles/soem.dir/soem/ethercatbase.c.o CMakeFiles/soem.dir/soem/ethercatcoe.c.o CMakeFiles/soem.dir/soem/ethercatconfig.c.o CMakeFiles/soem.dir/soem/ethercatdc.c.o CMakeFiles/soem.dir/soem/ethercateoe.c.o CMakeFiles/soem.dir/soem/ethercatfoe.c.o CMakeFiles/soem.dir/soem/ethercatmain.c.o CMakeFiles/soem.dir/soem/ethercatprint.c.o CMakeFiles/soem.dir/soem/ethercatsoe.c.o CMakeFiles/soem.dir/osal/linux/osal.c.o CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o CMakeFiles/soem.dir/oshw/linux/oshw.c.o  -lpthread -lrt 
