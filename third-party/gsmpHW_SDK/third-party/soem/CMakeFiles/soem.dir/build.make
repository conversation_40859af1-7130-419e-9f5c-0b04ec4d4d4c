# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lihy_ws/gsmp_v2-sdk

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lihy_ws/gsmp_v2-sdk

# Include any dependencies generated for this target.
include third-party/soem/CMakeFiles/soem.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third-party/soem/CMakeFiles/soem.dir/compiler_depend.make

# Include the progress variables for this target.
include third-party/soem/CMakeFiles/soem.dir/progress.make

# Include the compile flags for this target's objects.
include third-party/soem/CMakeFiles/soem.dir/flags.make

third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o: third-party/soem/soem/ethercatbase.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o -MF CMakeFiles/soem.dir/soem/ethercatbase.c.o.d -o CMakeFiles/soem.dir/soem/ethercatbase.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatbase.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatbase.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatbase.c > CMakeFiles/soem.dir/soem/ethercatbase.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatbase.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatbase.c -o CMakeFiles/soem.dir/soem/ethercatbase.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o: third-party/soem/soem/ethercatcoe.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o -MF CMakeFiles/soem.dir/soem/ethercatcoe.c.o.d -o CMakeFiles/soem.dir/soem/ethercatcoe.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatcoe.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatcoe.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatcoe.c > CMakeFiles/soem.dir/soem/ethercatcoe.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatcoe.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatcoe.c -o CMakeFiles/soem.dir/soem/ethercatcoe.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o: third-party/soem/soem/ethercatconfig.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o -MF CMakeFiles/soem.dir/soem/ethercatconfig.c.o.d -o CMakeFiles/soem.dir/soem/ethercatconfig.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatconfig.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatconfig.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatconfig.c > CMakeFiles/soem.dir/soem/ethercatconfig.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatconfig.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatconfig.c -o CMakeFiles/soem.dir/soem/ethercatconfig.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o: third-party/soem/soem/ethercatdc.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o -MF CMakeFiles/soem.dir/soem/ethercatdc.c.o.d -o CMakeFiles/soem.dir/soem/ethercatdc.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatdc.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatdc.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatdc.c > CMakeFiles/soem.dir/soem/ethercatdc.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatdc.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatdc.c -o CMakeFiles/soem.dir/soem/ethercatdc.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o: third-party/soem/soem/ethercateoe.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o -MF CMakeFiles/soem.dir/soem/ethercateoe.c.o.d -o CMakeFiles/soem.dir/soem/ethercateoe.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercateoe.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercateoe.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercateoe.c > CMakeFiles/soem.dir/soem/ethercateoe.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercateoe.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercateoe.c -o CMakeFiles/soem.dir/soem/ethercateoe.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o: third-party/soem/soem/ethercatfoe.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o -MF CMakeFiles/soem.dir/soem/ethercatfoe.c.o.d -o CMakeFiles/soem.dir/soem/ethercatfoe.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatfoe.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatfoe.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatfoe.c > CMakeFiles/soem.dir/soem/ethercatfoe.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatfoe.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatfoe.c -o CMakeFiles/soem.dir/soem/ethercatfoe.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o: third-party/soem/soem/ethercatmain.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o -MF CMakeFiles/soem.dir/soem/ethercatmain.c.o.d -o CMakeFiles/soem.dir/soem/ethercatmain.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatmain.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatmain.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatmain.c > CMakeFiles/soem.dir/soem/ethercatmain.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatmain.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatmain.c -o CMakeFiles/soem.dir/soem/ethercatmain.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o: third-party/soem/soem/ethercatprint.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o -MF CMakeFiles/soem.dir/soem/ethercatprint.c.o.d -o CMakeFiles/soem.dir/soem/ethercatprint.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatprint.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatprint.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatprint.c > CMakeFiles/soem.dir/soem/ethercatprint.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatprint.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatprint.c -o CMakeFiles/soem.dir/soem/ethercatprint.c.s

third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o: third-party/soem/soem/ethercatsoe.c
third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o -MF CMakeFiles/soem.dir/soem/ethercatsoe.c.o.d -o CMakeFiles/soem.dir/soem/ethercatsoe.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatsoe.c

third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/soem/ethercatsoe.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatsoe.c > CMakeFiles/soem.dir/soem/ethercatsoe.c.i

third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/soem/ethercatsoe.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/soem/ethercatsoe.c -o CMakeFiles/soem.dir/soem/ethercatsoe.c.s

third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o: third-party/soem/osal/linux/osal.c
third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o -MF CMakeFiles/soem.dir/osal/linux/osal.c.o.d -o CMakeFiles/soem.dir/osal/linux/osal.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/osal/linux/osal.c

third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/osal/linux/osal.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/osal/linux/osal.c > CMakeFiles/soem.dir/osal/linux/osal.c.i

third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/osal/linux/osal.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/osal/linux/osal.c -o CMakeFiles/soem.dir/osal/linux/osal.c.s

third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o: third-party/soem/oshw/linux/nicdrv.c
third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o -MF CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o.d -o CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/nicdrv.c

third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/oshw/linux/nicdrv.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/nicdrv.c > CMakeFiles/soem.dir/oshw/linux/nicdrv.c.i

third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/oshw/linux/nicdrv.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/nicdrv.c -o CMakeFiles/soem.dir/oshw/linux/nicdrv.c.s

third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o: third-party/soem/CMakeFiles/soem.dir/flags.make
third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o: third-party/soem/oshw/linux/oshw.c
third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o: third-party/soem/CMakeFiles/soem.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o -MF CMakeFiles/soem.dir/oshw/linux/oshw.c.o.d -o CMakeFiles/soem.dir/oshw/linux/oshw.c.o -c /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/oshw.c

third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/soem.dir/oshw/linux/oshw.c.i"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/oshw.c > CMakeFiles/soem.dir/oshw/linux/oshw.c.i

third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/soem.dir/oshw/linux/oshw.c.s"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/oshw/linux/oshw.c -o CMakeFiles/soem.dir/oshw/linux/oshw.c.s

# Object files for target soem
soem_OBJECTS = \
"CMakeFiles/soem.dir/soem/ethercatbase.c.o" \
"CMakeFiles/soem.dir/soem/ethercatcoe.c.o" \
"CMakeFiles/soem.dir/soem/ethercatconfig.c.o" \
"CMakeFiles/soem.dir/soem/ethercatdc.c.o" \
"CMakeFiles/soem.dir/soem/ethercateoe.c.o" \
"CMakeFiles/soem.dir/soem/ethercatfoe.c.o" \
"CMakeFiles/soem.dir/soem/ethercatmain.c.o" \
"CMakeFiles/soem.dir/soem/ethercatprint.c.o" \
"CMakeFiles/soem.dir/soem/ethercatsoe.c.o" \
"CMakeFiles/soem.dir/osal/linux/osal.c.o" \
"CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o" \
"CMakeFiles/soem.dir/oshw/linux/oshw.c.o"

# External object files for target soem
soem_EXTERNAL_OBJECTS =

third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/build.make
third-party/soem/libsoem.so: third-party/soem/CMakeFiles/soem.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/lihy_ws/gsmp_v2-sdk/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Linking C shared library libsoem.so"
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/soem.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third-party/soem/CMakeFiles/soem.dir/build: third-party/soem/libsoem.so
.PHONY : third-party/soem/CMakeFiles/soem.dir/build

third-party/soem/CMakeFiles/soem.dir/clean:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem && $(CMAKE_COMMAND) -P CMakeFiles/soem.dir/cmake_clean.cmake
.PHONY : third-party/soem/CMakeFiles/soem.dir/clean

third-party/soem/CMakeFiles/soem.dir/depend:
	cd /home/<USER>/lihy_ws/gsmp_v2-sdk && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/lihy_ws/gsmp_v2-sdk /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem /home/<USER>/lihy_ws/gsmp_v2-sdk /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem /home/<USER>/lihy_ws/gsmp_v2-sdk/third-party/soem/CMakeFiles/soem.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : third-party/soem/CMakeFiles/soem.dir/depend

