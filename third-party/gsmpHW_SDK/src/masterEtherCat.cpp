/*!
 * <AUTHOR>
 * @date 2023-8-9
 * @email <EMAIL>
 * @file masterEtherCat.cpp
 * @brief Provide a c++ api of EtherCat Master
 */


#include <chrono>
#include "masterEtherCat.h"

using namespace gsmp;

void masterEtherCat::checkLoop() {
    std::chrono::time_point<std::chrono::high_resolution_clock> startTime;
    long checkPeriod = 100; // checkPeriod ms检查一次
    int slave, errCount = 0;

    while (mRunning.load()) {
        startTime = std::chrono::high_resolution_clock::now();

        if (errCount > 20) { // 连续2秒接不全包, 可能断开连接了
            // possibly shut down
            printf("[EtherCAT] EtherCAT connection degraded, check the wire connection.\n");
            errCount = 0;
            std::unique_lock<std::mutex> lock(mStateMutex);
            mWorkingState = masterState::EXIST_DISCONNECT;
        }

        if (mRxMessage == nullptr || mTxMessage == nullptr) {
            auto endTime = startTime + std::chrono::milliseconds(checkPeriod);
            std::this_thread::sleep_until(endTime);
            continue;
        }

        if (mInOP && ((mWkc.load() < mExpectedWKC) || ec_group[0].docheckstate)) {
            /* one ore more slaves are not responding */
            ec_group[0].docheckstate = 0;
            ec_readstate();
            for (slave = 1; slave <= ec_slavecount; slave++){
                if ((ec_slave[slave].group == 0) && (ec_slave[slave].state != EC_STATE_OPERATIONAL)){
                    ec_group[0].docheckstate = 1;
                    if (ec_slave[slave].state == (EC_STATE_SAFE_OP + EC_STATE_ERROR)){
                        printf("[EtherCAT] Slave %d is in SAFE_OP + ERROR, attempting ack.\n", slave);
                        ec_slave[slave].state = (EC_STATE_SAFE_OP + EC_STATE_ACK);
                        ec_writestate(slave);
                        errCount++;
                    } else if (ec_slave[slave].state == EC_STATE_SAFE_OP) {
                        printf("[EtherCAT] Slave %d is in SAFE_OP, change to OPERATIONAL.\n", slave);
                        ec_slave[slave].state = EC_STATE_OPERATIONAL;
                        ec_writestate(slave);
                        errCount++;
                    } else if (ec_slave[slave].state > EC_STATE_NONE){
                        if (ec_reconfig_slave(slave, 500)) {
                            ec_slave[slave].islost = FALSE;
                            printf("[EtherCAT] Slave %d reconfigured\n", slave);
                        }
                    } else if (!ec_slave[slave].islost) {
                        /* re-check state */
                        ec_statecheck(slave, EC_STATE_OPERATIONAL, EC_TIMEOUTRET);
                        if (!ec_slave[slave].state) {
                            ec_slave[slave].islost = TRUE;
                            printf("[EtherCAT] Slave %d lost\n", slave);
                            errCount++;
                        }
                    }
                }
                if (ec_slave[slave].islost){
                    if (!ec_slave[slave].state){
                        if (ec_recover_slave(slave, 500)){
                            ec_slave[slave].islost = FALSE;
                            printf("[EtherCAT] Slave %d recovered\n", slave);
                        }
                    }else{
                        ec_slave[slave].islost = FALSE;
                        printf("[EtherCAT] Slave %d found\n", slave);
                    }
                }
            }

            if (!ec_group[0].docheckstate){
                printf("[EtherCAT] All slaves resumed OPERATIONAL.\n");
                std::unique_lock<std::mutex> lock(mStateMutex);
                mWorkingState = masterState::NORMAL;
            }
        }
        auto endTime = startTime + std::chrono::milliseconds(checkPeriod);
        std::this_thread::sleep_until(endTime);
    }
}

bool masterEtherCat::preInit(const std::string& network) {
    /* initialise SOEM, bind socket to network */
    if (ec_init(network.c_str())){
        printf("[EtherCAT] Initialization on device %s succeeded.\n", network.c_str());
        /* find and auto-config slaves */
        if (ec_config_init(FALSE) > 0) {
            printf("[EtherCAT] %d slaves found and configured.\n", ec_slavecount);
            if (ec_slavecount < mExpectedSlaveNums) {
                printf("[EtherCAT] Warning: Expected %d slaves, found %d.\n", mExpectedSlaveNums, ec_slavecount);
            }
            mSlaveCount = ec_slavecount;
            for (int slave_idx = 0; slave_idx < ec_slavecount; slave_idx++)
                ec_slave[slave_idx+1].CoEdetails &= ~ECT_COEDET_SDOCA;

            ec_config_map(&mIOmap);
            ec_configdc();

            printf("[EtherCAT] Mapped slaves.\n");
            /* wait for all slaves to reach SAFE_OP state */
            ec_statecheck(0, EC_STATE_SAFE_OP, EC_TIMEOUTSTATE * mExpectedSlaveNums);

            for (int slave_idx = 0; slave_idx < ec_slavecount; slave_idx++){
                printf("  [SLAVE %d]\n", slave_idx);
                printf("  IN  %d bytes, %d bits\n", ec_slave[slave_idx].Ibytes, ec_slave[slave_idx].Ibits);
                printf("  OUT %d bytes, %d bits\n", ec_slave[slave_idx].Obytes, ec_slave[slave_idx].Obits);
                printf("\n");
            }

            printf("[EtherCAT] segments : %d : %d %d %d %d\n", ec_group[0].nsegments, ec_group[0].IOsegment[0], ec_group[0].IOsegment[1], ec_group[0].IOsegment[2], ec_group[0].IOsegment[3]);
            printf("[EtherCAT] Requesting operational state for all slaves...\n");

            mExpectedWKC = (ec_group[0].outputsWKC * 2) + ec_group[0].inputsWKC;
            printf("[EtherCAT] Calculated work counter %d\n", mExpectedWKC);
            ec_slave[0].state = EC_STATE_OPERATIONAL;
            /* send one valid process data to make outputs in slaves happy*/
            ec_send_processdata();
            ec_receive_processdata(EC_TIMEOUTRET);
            /* request OP state for all slaves */
            ec_writestate(0);
            unsigned int chk = 40;
            /* wait for all slaves to reach OP state */
            do {
                ec_send_processdata();
                ec_receive_processdata(EC_TIMEOUTRET);
                ec_statecheck(0, EC_STATE_OPERATIONAL, 50000);
            } while (chk-- && (ec_slave[0].state != EC_STATE_OPERATIONAL));

            if (ec_slave[0].state == EC_STATE_OPERATIONAL) {
                printf("[EtherCAT] Operational state reached for all slaves.\n");
                mInOP = 1;
                std::unique_lock<std::mutex> lock(mStateMutex);
                mWorkingState = masterState::NORMAL;
                return true;
            } else {
                printf("[EtherCAT] Not all slaves reached operational state.\n");
                ec_readstate();
                for (int i = 1; i <= ec_slavecount; i++) {
                    if (ec_slave[i].state != EC_STATE_OPERATIONAL) {
                        printf("[EtherCAT] Slave %d State=0x%2.2x StatusCode=0x%4.4x : %s\n", i, ec_slave[i].state, ec_slave[i].ALstatuscode, ec_ALstatuscode2string(ec_slave[i].ALstatuscode));
                    }
                }
                std::unique_lock<std::mutex> lock(mStateMutex);
                mWorkingState = masterState::EXIST_DISCONNECT;
            }
        } else {
            printf("[EtherCAT] No slaves found!\n");
        }
    } else {
        mRunning.store(false);
        printf("[EtherCAT] No socket connection on %s\n", network.c_str());
    }
    return false;
}

/************************************ Class Member Function *************************************/
void masterEtherCat::bindWithSlave(std::shared_ptr<slaveBase> Slave) {
    static int boundSlaveCount = 0;
    printf("[EtherCAT] Already bind %d Slaves.\n", boundSlaveCount+1);
    mSlaveContainer.push_back(Slave);
    boundSlaveCount++;
}

bool masterEtherCat::init(const std::string& devName,int baudrate) {
    static_cast<void>(baudrate);
    if (mRxMessage != nullptr || mTxMessage != nullptr) {
        printf("[EtherCAT] masterEtherCat::init() repeated init!\n");
        return false;
    }

    if (mSlaveContainer.empty()) {
        printf("[EtherCAT] no Slave was bound, please call masterEtherCat::bindWithSlave first!\n");
        return false;
    }

    if (devName.empty()) {
        printf("[EtherCAT] masterEtherCat::init() network is empty!\n");
        return false;
    }

    mExpectedSlaveNums = mSlaveContainer.size();
    //mNetwork = devName;

    printf("[EtherCAT] Initializing EtherCAT.\n");
    uint8_t i;
    bool checkResult;
    for (i = 1; i < 10; i++) {
        printf("[EtherCAT] Attempting to start EtherCAT, try %d of 10.\n", i);
        checkResult = preInit(devName);
        if (checkResult)
            break;
        std::this_thread::sleep_for(std::chrono::duration<double>(1));  // 休眠1s
    }

    if (checkResult) {
        mTxMessage = new EtherCAT_TxMsg[mExpectedSlaveNums];
        mRxMessage = new EtherCAT_RxMsg[mExpectedSlaveNums];

        mCheckThread = std::thread(&gsmp::masterEtherCat::checkLoop, this);
        printf("[EtherCAT] EtherCAT successfully initialized on attempt %d \n", i);
        return true;
    } else {
        printf("[EtherCAT] Failed to initialize EtherCAT after 10 tries. \n");
    }
    return false;
}

void masterEtherCat::send() {
    if (mSlaveContainer.empty() || ec_slavecount == 0) {
        printf("[EtherCAT] No Slave was bound, please call masterEtherCat::bindWithSlave first or check ethercat connection!\n");
        return;
    }
    auto Slave_count = mSlaveContainer.size() < ec_slavecount? mSlaveContainer.size(): ec_slavecount;

    for (uint8_t slave = 0; slave < Slave_count; ++slave) {
        memcpy(&mTxMessage[slave], mSlaveContainer[slave]->updateSlaveCommand(), sizeof(EtherCAT_TxMsg));
        auto slave_dest = (EtherCAT_TxMsg *)(ec_slave[slave + 1].outputs);
        if (slave_dest)
            *(EtherCAT_TxMsg *)(ec_slave[slave + 1].outputs) = mTxMessage[slave];
    }
    ec_send_processdata();
}

void masterEtherCat::recv(bool verbose) {
    auto Slave_count = mSlaveContainer.size() < ec_slavecount? mSlaveContainer.size(): ec_slavecount;
    // for (int i = 0; i < 2; ++i) {
    //     std::memset(ec_slave[i + 1].inputs ,0, Slave_count * sizeof(EtherCAT_Msg));  // 86 字节一个从站
    // }

    mWkc.store(ec_receive_processdata(EC_TIMEOUTRET * 2000));
    static uint8_t wkcErrCount = 0;

    if (wkcErrCount > 20) {
        printf("[EtherCAT] Error count too high!\n");
        wkcErrCount = 0;
    }

    if (mSlaveContainer.empty() || ec_slavecount == 0) {
        printf("[EtherCAT] No Slave was bound, please call masterEtherCat::bindWithSlave first or check ethercat connection!\n");
        return;
    }

    std::memset(mRxMessage ,0, Slave_count * sizeof(EtherCAT_RxMsg)); 

    for (uint8_t slave = 0; slave < Slave_count; ++slave) {
        auto *slave_src = (EtherCAT_RxMsg *)(ec_slave[slave + 1].inputs);
        if (slave_src){
            mRxMessage[slave] = *(EtherCAT_RxMsg *)(ec_slave[slave + 1].inputs);

            if (mSlaveContainer[slave] == nullptr) {
                printf("[EtherCAT] mSlaveContainer[%d] is nullptr\n", slave);
                continue;
            }

            /// 解析接收的数据
            mSlaveContainer[slave]->parseImuMessages(&(mRxMessage[slave]), slave, verbose);
            auto *RxMessage = (masterRxMsg *)(&(mRxMessage[slave]));
            mSlaveContainer[slave]->parseMotorMessages(RxMessage, slave, verbose);
            mSlaveContainer[slave]->parseBatteryMessages(RxMessage, slave, verbose);
        }            
        else
            printf("[EtherCAT] Slave messages have not been initialized\n");
    }

    if (mWkc.load() < mExpectedWKC) {
        printf("[EtherCat] expected wkc is %d, actual wkc is %d\n", mExpectedWKC, mWkc.load());
        printf("\x1b[31m[EtherCAT] Dropped packet (Bad WKC!)\x1b[0m\n");
        wkcErrCount++;
    }
}

masterEtherCat::masterEtherCat(): mInOP(0),
                                  mWkc(0),
                                  mRunning(true),
                                  mSlaveCount(0),
                                  mExpectedWKC(0),
                                  mWorkingState(masterState::ALL_DISCONNECT) {
    mExpectedSlaveNums = 0;
    mSlaveContainer.clear();
    mTxMessage = nullptr;
    mRxMessage = nullptr;
}

masterEtherCat::~masterEtherCat() {
    mRunning.store(false);
    delete []mRxMessage;
    delete []mTxMessage;
    mSlaveContainer.clear();

    if (mCheckThread.joinable())
        mCheckThread.join();
}

uint8_t masterEtherCat::getSlaveCount() const {
    return mSlaveCount;
}

masterState masterEtherCat::getMasterWorkingState(uint8_t index) {
    static_cast<void>(index);
    std::unique_lock<std::mutex> lock(mStateMutex);
    return mWorkingState;
}
