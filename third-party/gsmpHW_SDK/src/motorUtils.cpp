
/*!
 * <AUTHOR>
 * @date 2023-8-12
 * @email <EMAIL>
 * @brief Provide a template class of Motor as ethercat Slave
 */

#include "motorUtils.h"

float gsmp::saturate(float value, float min_value, float max_value) {
    if (value >= max_value) {
        return max_value;
    } else if (value <= min_value) {
        return min_value;
    } else {
        return value;
    }
}

uint32_t gsmp::float_to_uint(float x, float x_min, float x_max, uint8_t bits){
    /// Converts a float to an unsigned int, given range and number of bits ///
    float span = x_max - x_min;
    float offset = x_min;
    return (uint32_t) ((x - offset) * ((float) ((1 << bits) - 1)) / span);
}

float gsmp::uint_to_float(uint32_t x_int, float x_min, float x_max, uint8_t bits) {
    /// converts unsigned int to float, given range and number of bits ///
    float span = x_max - x_min;
    float offset = x_min;
    return ((float) x_int) * span / ((float) ((1 << bits) - 1)) + offset;
}