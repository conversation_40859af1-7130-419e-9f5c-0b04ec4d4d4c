/*========================= Gamepad Control ================================*/
 /* @brief   罗技遥控器的驱动库，库目前支持罗技遥控器型号：F710、F310。
  * @note 
  *         - 注意按键值所对应的意义。
  *         - 当手柄 Mode为黄灯时，左边上下左右按键与左边遥感值互换，按键按下为对应遥感边界值（+-1）
  *           遥感推至极限为对应按键值。
  *         - 一般情况下使用非黄灯模式。
  * @method 
  *         - 1. 手柄驱动路径     ： char path[] = "/dev/input/js0";
  *         - 2. 定义一个遥控器类  : Logitech Logitech(path);
  *         - 3. 遥控器类初始化    : Logitech.init();
  *         - 4. 线程中进行数据接收 : Logitech.listen_input();
  * @warning 
  *         - At least C++11 is required.	
  *         - Only linux platform is supported for now.
*/ 												 
/*========================= Gamepad Control ================================*/

/* Includes ----------------------------------------------------------------*/
#include "logitechJoy.h"

using namespace gsmp;
// using namespace std;

Logitech::Logitech()
{
    // dev = device;
    memset(buf, 0, sizeof buf);
}

int Logitech::init(char* device)
{
    dev = device;
    fd = open(dev, O_RDONLY);
    if (fd == -1) {
        fprintf(stderr, "Cannot open %s: %s.\n", dev, strerror(errno));
        return EXIT_FAILURE;
    }
    else{
        printf("[JOY] initialize %s successfully.\n",dev);
    }
    /*Key Status*/

    /* 0 is released */
    /* 1 is press    */
    Keystate_map[JSKEY_A] =0;
    Keystate_map[JSKEY_B] =0;
    Keystate_map[JSKEY_X] =0;
    Keystate_map[JSKEY_Y] =0;

    /* 0 is released */
    /* 1 is press    */
    Keystate_map[JSKEY_LB] =0;
    Keystate_map[JSKEY_RB] =0;
   
    /* 0 is released */
    /* 1 is press    */
    Keystate_map[JSKEY_BACK] =0;
    Keystate_map[JSKEY_START] =0;
    Keystate_map[JSKEY_HOME] =0;
    
    /*  0 is released */
    /* -1 is the left or up button is pressed */
    /*  1 is the right or down button is pressed*/
    Keystate_map[JSKEY_CROSS_X] =0;
    Keystate_map[JSKEY_CROSS_Y] =0;

    /* the result is the value of the key(0~99)*/
    Keystate_map[JSKEY_LT] =0;
    Keystate_map[JSKEY_RT] =0;

    /* the result is the value of the key(-100~100)*/
    Keystate_map[JSKEY_LEFTSTICK_X] =0;
    Keystate_map[JSKEY_LEFTSTICK_Y] =0;
    Keystate_map[JSKEY_RIGHTSTICK_X] =0;
    Keystate_map[JSKEY_RIGHTSTICK_Y] =0;

    return 0;
}

void Logitech::listen_input()
{
    // while (1) {
        memset(buf, 0, sizeof buf);
        n = read(fd, &buf, sizeof buf);
        n = n / sizeof(int);
        if (n == (ssize_t)-1) {
            // if (errno == EINTR)
            //     continue;
            // else
            //     break;
          return;  
        }

        unsigned short btn = buf[1] >> 16;
        short val = (short)(buf[1] & 0xffff);

         /*Test for button ID*/
         //cout<<"0x"<<hex<<btn<<endl;

        if (btn == JSKEY_LT || btn == JSKEY_RT)
        {
            unsigned short prs_val = val + 32768;
            val = (unsigned short) (((long)prs_val)*100/65536);
            Keystate_map[btn]= val;
        }
        else if (btn == JSKEY_LEFTSTICK_X || btn == JSKEY_LEFTSTICK_Y ||
                 btn == JSKEY_RIGHTSTICK_X || btn == JSKEY_RIGHTSTICK_Y)
        {
            /* y-axis reverse */
            if(btn==JSKEY_LEFTSTICK_Y||btn == JSKEY_RIGHTSTICK_Y)
            {val=(-1)*val;}
            val = val*100/32767;
            Keystate_map[btn]= val;
        }
        else
        {
            switch (val)
            {
            case JSKEY_PRESS:
                Keystate_map[btn]=1;
                break;
            case JSKEY_RELEASE:
                Keystate_map[btn]=0;
                break;
            case JSKEY_CROSS_LOW_VALUE:
                Keystate_map[btn]=-1;
                break;
            case JSKEY_CROSS_HIGH_VALUE:
                Keystate_map[btn]=1;
                break;
            default:
                break;
            }
            /* y-axis reverse */
            if(btn==JSKEY_CROSS_Y)
            {Keystate_map[btn]=(-1)*Keystate_map[btn];}
        }
        joy_callback();
        // print_key_state();
    // }
}

void Logitech::print_key_state()
{
   std::cout<<std::endl;
   std::cout<<"JSKEY_A = "<<Keystate_map[JSKEY_A]<<std::endl;
   std::cout<<"JSKEY_B = "<<Keystate_map[JSKEY_B]<<std::endl;
   std::cout<<"JSKEY_X = "<<Keystate_map[JSKEY_X]<<std::endl;
   std::cout<<"JSKEY_Y = "<<Keystate_map[JSKEY_Y]<<std::endl;

   std::cout<<"JSKEY_LB = "<<Keystate_map[JSKEY_LB]<<std::endl;
   std::cout<<"JSKEY_RB = "<<Keystate_map[JSKEY_RB]<<std::endl;
   std::cout<<"JSKEY_BACK = "<<Keystate_map[JSKEY_BACK]<<std::endl;
   std::cout<<"JSKEY_START = "<<Keystate_map[JSKEY_START]<<std::endl;
   std::cout<<"JSKEY_HOME = "<<Keystate_map[JSKEY_HOME]<<std::endl;

   std::cout<<"JSKEY_LT = "<<Keystate_map[JSKEY_LT]<<std::endl;
   std::cout<<"JSKEY_RT = "<<Keystate_map[JSKEY_RT]<<std::endl;

   std::cout<<"JSKEY_CROSS_X = "<<Keystate_map[JSKEY_CROSS_X]<<std::endl;
   std::cout<<"JSKEY_CROSS_Y = "<<Keystate_map[JSKEY_CROSS_Y]<<std::endl;   

   std::cout<<"JSKEY_LEFTSTICK_X  = "<<Keystate_map[JSKEY_LEFTSTICK_X] <<"     JSKEY_LEFTSTICK_Y   = "<<Keystate_map[JSKEY_LEFTSTICK_Y]<<std::endl;
   std::cout<<"JSKEY_RIGHTSTICK_X = "<<Keystate_map[JSKEY_RIGHTSTICK_X]<<"     JSKEY_RIGHTSTICK_Y = "<<Keystate_map[JSKEY_RIGHTSTICK_Y]<<std::endl;
}

void Logitech::joy_callback() {   
    // std::cout << "joystick recieved" << std::endl;
    // 摇杆和扳机轴数据（根据实际手柄映射调整索引）
    data.left_stick_x  = Keystate_map[JSKEY_LEFTSTICK_X] * 0.01f;  // 左摇杆 X [-1,1]
    data.left_stick_y  = Keystate_map[JSKEY_LEFTSTICK_Y] * 0.01f;  // 左摇杆 Y [-1,1]
    data.right_stick_x = Keystate_map[JSKEY_RIGHTSTICK_X] * 0.01f;  // 右摇杆 X [-1,1]
    data.right_stick_y = Keystate_map[JSKEY_RIGHTSTICK_Y] * 0.01f;  // 右摇杆 Y [-1,1]
    data.left_trigger  = Keystate_map[JSKEY_LT] * 0.01f;  // LT [0,1]
    data.right_trigger = Keystate_map[JSKEY_RT] * 0.01f;  // RT [0,1]

    // 按钮数据（索引需通过 `rostopic echo /joy` 确认）
    data.button_A     = Keystate_map[JSKEY_A];  // A
    data.button_B     = Keystate_map[JSKEY_B];  // B
    data.button_X     = Keystate_map[JSKEY_X];  // X
    data.button_Y     = Keystate_map[JSKEY_Y];  // Y
    data.button_LB    = Keystate_map[JSKEY_LB];  // LB
    data.button_RB    = Keystate_map[JSKEY_RB];  // RB
    data.button_back  = Keystate_map[JSKEY_BACK];  // BACK
    data.button_start = Keystate_map[JSKEY_START];  // START
    data.button_guide = Keystate_map[JSKEY_HOME];  // LOGITECH
    // data.button_L3    = Keystate_map[JSKEY_L3];  // 左摇杆按下
    // data.button_R3    = Keystate_map[JSKEY_R3]; // 右摇杆按下

    if(Keystate_map[JSKEY_CROSS_Y] == 1)  data.dpad_up = true;
    if(Keystate_map[JSKEY_CROSS_Y] == -1) data.dpad_down = true;
    if(Keystate_map[JSKEY_CROSS_X] == 1)  data.dpad_right = true;
    if(Keystate_map[JSKEY_CROSS_X] == -1) data.dpad_left = true;
    
}