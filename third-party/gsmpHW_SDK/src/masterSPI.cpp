/*!
 * <AUTHOR>
 * @date 2025-2-14
 * @file masterSPI.cpp
 * @brief Provide a c++ api of SPI Master
 */


//#include <chrono>
#include "masterSPI.h"

using namespace gsmp;


/************************************ Class Member Function *************************************/
/*!
 * @brief SPI message checksum
 * @param data : input
 * @param len : length (in 16-bit words)
 * @return checksum
 */
// uint16_t masterSPI::xor_checksum(uint16_t *data, size_t len) {
//   uint16_t t = 0;
//   for (size_t i = 0; i < len; i++) t = t ^ data[i];
//   return t;
// }

void masterSPI::bindWithSlave(std::shared_ptr<slaveBase> Slave) {
//    static int boundSlaveCount = 0;
    printf("[SPI] Already bind %d Slaves.\n", mSlaveCount+1);
    mSlaveContainer.push_back(Slave);
    mSlaveCount++;
}

bool masterSPI::init(const std::string& devName,int baudrate) {
    static_cast<void>(baudrate);
    if (mRxMessage != nullptr || mTxMessage != nullptr) {
        printf("[SPI] masterSPI::init() repeated init!\n");
        return false;
    }

    if (mSlaveContainer.empty()) {
        printf("[SPI] no Slave was bound, please call masterSPI::bindWithSlave first!\n");
        return false;
    }

    if (devName.empty()) {
        printf("[SPI] masterSPI::init() network is empty!\n");
        return false;
    }

    mExpectedSlaveNums = mSlaveContainer.size();
    //mNetwork = devName;

    printf("[SPI] Initializing SPI.\n"); 
    // 打开SPI设备文件
    fd = open(devName.c_str(), O_RDWR);
    if (fd < 0) {
        perror("\033[31m[SPI] Can't open SPI device\033[0m");
        return false;
    }
    // 设置SPI模式
    if (ioctl(fd, SPI_IOC_WR_MODE, &mode) == -1) {
        perror("\033[31m[SPI] Can't set SPI mode\033[0m");
        close(fd);
        return false;
    }
    // 设置SPI位宽
    if (ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits) == -1) {
        perror("\033[31m[SPI] Can't set bits per word\033[0m");
        close(fd);
        return false;
    }
    // 设置SPI最大时钟频率
    if (ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed) == -1) {
        perror("\033[31m[SPI] Can't set max speed hz\033[0m");
        close(fd);
        return false;
    }

    mTxMessage = new masterTxMsg[mExpectedSlaveNums];
    mRxMessage = new masterRxMsg[mExpectedSlaveNums];

    printf("[SPI] SPI successfully initialized\n");

    mWorkingState = masterState::NORMAL;

    return true;
}

void masterSPI::transfer(bool verbose){
    std::unique_lock<std::mutex> lock(mTransferMutex);
    if (mSlaveContainer.empty()) {
        printf("[EtherCAT] No Slave was bound, please call etherCatMaster::bindWithSlave first or check ethercat connection!\n");
        return;
    }    

    size_t Slave_count = mSlaveContainer.size();

    for (uint8_t slave = 0; slave < Slave_count; ++slave) {
        memcpy(&mTxMessage[slave], mSlaveContainer[slave]->updateSlaveCommand(), sizeof(masterTxMsg));
        // 添加数据校验值
        mTxMessage[slave].checksum = xor_checksum((uint16_t *)&mTxMessage[slave], sizeof(masterTxMsg)/2-1); 

        struct spi_ioc_transfer transfer = {
        .tx_buf = (unsigned long)&mTxMessage[slave],
        .rx_buf = (unsigned long)&mRxMessage[slave],
        .len = sizeof(masterTxMsg),
        .speed_hz = speed,
        .delay_usecs = 0,
        .bits_per_word = bits,
        };

        if (ioctl(fd, SPI_IOC_MESSAGE(1), &transfer) < 0) {
            perror("\033[31m[SPI] SPI transfer failed\033[0m");
            return;
        }
        else{
            // 数据校验
            uint16_t calc_checksum = xor_checksum((uint16_t *)&mRxMessage[slave], sizeof(masterRxMsg)/2-1); 
            if (calc_checksum != mRxMessage[slave].checksum){
                printf("\033[31m[SPI] SPI ERROR BAD CHECKSUM, GOT 0x%hx EXPECTED 0x%hx\033[0m\n", calc_checksum,mRxMessage[slave].checksum);
                return;
            }
            else{
                auto *RxMessage = (masterRxMsg *)(&(mRxMessage[slave]));
                mSlaveContainer[slave]->parseMotorMessages(RxMessage, slave, verbose);
                mSlaveContainer[slave]->parseBatteryMessages(RxMessage, slave, verbose);
            }           
        }       
    }
}

masterSPI::masterSPI(): mSlaveCount(0),
                        mWorkingState(masterState::ALL_DISCONNECT) {
    mode = SPI_MODE_3;
    speed = 10000000;
    bits = 8;                                
    mExpectedSlaveNums = 0;
    mSlaveContainer.clear();
    mTxMessage = nullptr;
    mRxMessage = nullptr;
}

masterSPI::~masterSPI() {
    delete []mRxMessage;
    delete []mTxMessage;
    mSlaveContainer.clear();
}

uint8_t masterSPI::getSlaveCount() const {
    return mSlaveCount;
}

masterState masterSPI::getMasterWorkingState(uint8_t index) {
    static_cast<void>(index);
    std::unique_lock<std::mutex> lock(mStateMutex);
    return mWorkingState;
}
