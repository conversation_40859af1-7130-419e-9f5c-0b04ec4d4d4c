/*!
 * <AUTHOR>
 * @date   2024-1-10
 * @brief XiaoMi Motors sdk
 */

#include <cmath>
#include <chrono>
#include "motorCyber.h"
#include "motorUtils.h"


using namespace gsmp;

motorCyber::motorCyber() : motorBase() {    
    mCanMasterId = 0xFD; //Master ID,0~FD任意设置
    /// MCU端电机排列顺序设置的id（can:1->2->3->4）              
    mMotorId[0] = 0x01;    //can1                 
    mMotorId[1] = 0x02;                    
    mMotorId[2] = 0x03;    
    mMotorId[3] = 0x01;    //can2
    mMotorId[4] = 0x02;
    mMotorId[5] = 0x03;
    mMotorId[6] = 0x01;    //can3
    mMotorId[7] = 0x02;                    
    mMotorId[8] = 0x03;
    mMotorId[9] = 0x01;    //can4
    mMotorId[10] = 0x02;
    mMotorId[11] = 0x03;
}

/// @brief 将反Z字型排列的电机指令按MCU端的电机顺序调整
std::array<motorCommand, 12> motorCyber::adjustMotorCommandIndex(std::array<motorCommand, 12> &legCommand) {
    std::array<motorCommand, 12> mtrCommand;
    ///根据MCU端实际的顺序调整，电机id详细定义见motorCyber()
    // for(int i=0;i<MOTOR_NM;++i){
    //    mtrCommand[i] = legCommand[mMotorId[i]-1];       
    // }
    mtrCommand[0] = legCommand[3];
    mtrCommand[1] = legCommand[4];
    mtrCommand[2] = legCommand[5];
    mtrCommand[3] = legCommand[9];
    mtrCommand[4] = legCommand[10];
    mtrCommand[5] = legCommand[11];
    mtrCommand[6] = legCommand[6];
    mtrCommand[7] = legCommand[7];
    mtrCommand[8] = legCommand[8];
    mtrCommand[9] = legCommand[0];
    mtrCommand[10] = legCommand[1];
    mtrCommand[11] = legCommand[2];

    return mtrCommand;
}

/// @brief 将从MCU端接收的电机数据按反Z字型排列调整
std::array<canMsg, 12> motorCyber::adjustMotorDataIndex(masterRxMsg *rxMessage) {
    std::array<canMsg, 12> mtrData;
    // for(int i=0;i<MOTOR_NM;++i){
    //     mtrData[mMotorId[i]-1] = rxMessage->motor[i];
    // }
    mtrData[3] = rxMessage->motor[0];
    mtrData[4] = rxMessage->motor[1];
    mtrData[5] = rxMessage->motor[2];
    mtrData[9] = rxMessage->motor[3];
    mtrData[10] = rxMessage->motor[4];
    mtrData[11] = rxMessage->motor[5];
    mtrData[6] = rxMessage->motor[6];
    mtrData[7] = rxMessage->motor[7];     
    mtrData[8] = rxMessage->motor[8];   
    mtrData[0] = rxMessage->motor[9];
    mtrData[1] = rxMessage->motor[10];    
    mtrData[2] = rxMessage->motor[11];  
   
    return mtrData;
}

bool motorCyber::enableAllMotors() {
    uint8_t command_flag = 0x03;
    auto master_id = static_cast<uint16_t>(mCanMasterId);

    static bool firstPrint = true;

    for (uint8_t index = 0; index < 12; index++) {
        uint32_t command_id = 0;
        command_id |= static_cast<uint32_t>(command_flag) << 24;
        command_id |= static_cast<uint32_t>(master_id) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[index]);

        mSlaveCommand->motor[index].sts = 0x00;
        mSlaveCommand->motor[index].rtr = 0x00;
        mSlaveCommand->motor[index].ide = 0x01;
        mSlaveCommand->motor[index].id = command_id;
        mSlaveCommand->motor[index].dlc = 8;

        mSlaveCommand->motor[index].data[0] = 0x00;
        mSlaveCommand->motor[index].data[1] = 0x00;
        mSlaveCommand->motor[index].data[2] = 0x00;
        mSlaveCommand->motor[index].data[3] = 0x00;
        mSlaveCommand->motor[index].data[4] = 0x00;
        mSlaveCommand->motor[index].data[5] = 0x00;
        mSlaveCommand->motor[index].data[6] = 0x00;
        mSlaveCommand->motor[index].data[7] = 0x00;
    }
    if (firstPrint) {
        printf("[MotorCyber] CyberGear motors enable command sent!\n");
        firstPrint = false;
    }
    return true;
}

bool motorCyber::disableAllMotors() {
    uint8_t command_flag = 0x04;
    auto master_id = static_cast<uint16_t>(mCanMasterId);

    static bool firstPrint = true;

    for (uint8_t index = 0; index < 12; index++) {
        uint32_t command_id = 0;
        command_id |= static_cast<uint32_t>(command_flag) << 24;
        command_id |= static_cast<uint32_t>(master_id) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[index]);

        mSlaveCommand->motor[index].sts = 0x00;
        mSlaveCommand->motor[index].rtr = 0x00;
        mSlaveCommand->motor[index].ide = 0x01;
        mSlaveCommand->motor[index].id = command_id;
        mSlaveCommand->motor[index].dlc = 8;

        mSlaveCommand->motor[index].data[0] = 0x00;
        mSlaveCommand->motor[index].data[1] = 0x00;
        mSlaveCommand->motor[index].data[2] = 0x00;
        mSlaveCommand->motor[index].data[3] = 0x00;
        mSlaveCommand->motor[index].data[4] = 0x00;
        mSlaveCommand->motor[index].data[5] = 0x00;
        mSlaveCommand->motor[index].data[6] = 0x00;
        mSlaveCommand->motor[index].data[7] = 0x00;
    }

    if (firstPrint) {
        printf("[MotorCyber] CyberGear motors disable command sent!\n");
        firstPrint = false;
    }
    return true;
}

void motorCyber::setMotorCommand(std::array<motorCommand, 12> legCommand) {
    uint16_t kp_int;
    uint16_t kd_int;
    uint16_t pos_int;
    uint16_t spd_int;
    uint16_t tor_int;

    uint8_t command_flag = 0x01;    // 电机运动控制模式

    std::array<motorCommand, 12> mtrCommand = adjustMotorCommandIndex(legCommand);

    for (uint8_t motor_index = 0; motor_index < 12; ++motor_index) {
        uint32_t command_id = 0;
//        double tor = saturate(command[motor_index].tau_ff, -12, 12);
        tor_int = float_to_uint(mtrCommand[motor_index].tau_ff, T_MIN,T_MAX, 16);
        command_id |= static_cast<uint32_t>(command_flag) << 24;
        command_id |= static_cast<uint32_t>(tor_int) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[motor_index]);

        mSlaveCommand->motor[motor_index].id = command_id;        // 电机id根据实际情况调整
        mSlaveCommand->motor[motor_index].sts = 0x00;
        mSlaveCommand->motor[motor_index].rtr = 0x00;
        mSlaveCommand->motor[motor_index].ide = 0x01;
        mSlaveCommand->motor[motor_index].dlc = 8;

        kp_int = float_to_uint(mtrCommand[motor_index].kp, KP_MIN, KP_MAX, 16);
        kd_int = float_to_uint(mtrCommand[motor_index].kd, KD_MIN, KD_MAX, 16);
        pos_int = float_to_uint(mtrCommand[motor_index].des_pos, P_MIN, P_MAX, 16);
        spd_int = float_to_uint(mtrCommand[motor_index].des_vel, V_MIN, V_MAX, 16);

        mSlaveCommand->motor[motor_index].data[0] = pos_int >> 8;
        mSlaveCommand->motor[motor_index].data[1] = pos_int & 0xFF;
        mSlaveCommand->motor[motor_index].data[2] = spd_int >> 8;
        mSlaveCommand->motor[motor_index].data[3] = spd_int & 0xFF;
        mSlaveCommand->motor[motor_index].data[4] = kp_int >> 8;
        mSlaveCommand->motor[motor_index].data[5] = kp_int & 0xFF;
        mSlaveCommand->motor[motor_index].data[6] = kd_int >> 8;
        mSlaveCommand->motor[motor_index].data[7] = kd_int & 0xFF;
    }
}

void motorCyber::parseMotorMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose) {
//    static unsigned int canDisconnectCount[6]{0};
//    static unsigned int canConnectCount[6]{0};

    std::array<canMsg,12> mtrData = adjustMotorDataIndex(rxMessage);

    for (uint8_t i = 0; i < 12; ++i) {
        uint8_t command_id = (mtrData[i].id & 0x1F000000)>>24;

        if(mtrData[i].sts > 0){
            printf("[MotorCyber] \033[31m 从站 %d 上的 %d 号电机通讯丢失\n\033[0m", slaveIdx + 1, i + 1);
            mMotorConnectState[i] = false;
        } 
        else {
            if (!mMotorConnectState[i]) {
                printf("[MotorCyber] \033[32m 从站 %d 上的 %d 号电机通讯恢复\n\033[0m", slaveIdx + 1, i + 1);
                mMotorConnectState[i] = true;
            }
            ///解析电机数据
            if (command_id == 0x02) {
                uint8_t errCode = (mtrData[i].id & 0x001F0000)>>16;            

                auto pos_int = static_cast<uint16_t>(mtrData[i].data[1]);
                pos_int |= static_cast<uint16_t>(mtrData[i].data[0]) << 8;
                auto vel_int = static_cast<uint16_t>(mtrData[i].data[3]);
                vel_int |= static_cast<uint16_t>(mtrData[i].data[2]) << 8;

                auto tor_int = static_cast<uint16_t>(mtrData[i].data[5]);
                tor_int |= static_cast<uint16_t>(mtrData[i].data[4]) << 8;
                auto temp_int = static_cast<uint16_t>(mtrData[i].data[7]);
                temp_int |= static_cast<uint16_t>(mtrData[i].data[6]) << 8;

                mMotorStates[i].pos = uint_to_float(pos_int, P_MIN, P_MAX, 16);
                mMotorStates[i].vel = uint_to_float(vel_int, V_MIN, V_MAX, 16);
                mMotorStates[i].tau = uint_to_float(tor_int, T_MIN, T_MAX, 16);
                mMotorStates[i].temp = temp_int / 10.0f;

                if (errCode&0x01) {
                    printf("[MotorCyber] \033[31m 从站 %d 上的 %d 号电机电压过低\n\033[0m", slaveIdx + 1,  i + 1);
                    mMotorStates[i].err = motorErr::UNDER_VOLTAGE;
                }

                if (errCode&0x02) {
                    printf("[MotorCyber] \033[31m 从站 %d 上的 %d 号电机电流过大\n\033[0m", slaveIdx + 1,  i + 1);
                    mMotorStates[i].err = motorErr::OVER_CURRENT;
                }

                if (errCode&0x04) {
                    printf("[MotorCyber] \033[31m 从站 %d 上的 %d 号电机温度过高\n\033[0m", slaveIdx + 1,  i + 1);
                    mMotorStates[i].err = motorErr::OVER_HEAT;
                }

                if ((errCode&0x08) | (errCode&0x10)) {
                    printf("[MotorCyber] \033[31m 从站 %d 上的 %d 号电机编码器错误\n\033[0m", slaveIdx + 1,  i + 1);
                    mMotorStates[i].err = motorErr::ENCODER_ERROR;
                }
            }

            if(verbose){
                printf("[MotorCyber] motor%2d: pos=%-4.2f, vel=%-4.2f, cur=%-4.2f, tmp=%-3.f\n",i+1,mMotorStates[i].pos, mMotorStates[i].vel, mMotorStates[i].tau, mMotorStates[i].temp);
            }
        }
    }
}

void motorCyber::setMotorImpedance() {
    std::array<gsmp::motorCommand, 12> command_kd;
    float passive_kd = (float)(KD_MAX/2);

    for (uint8_t i = 0; i < 12; ++i) {
        command_kd[i].kd = passive_kd;
    }

    motorCyber::setMotorCommand(command_kd);
}

// void motorCyber::getMotorsCanID() {
//     uint8_t command_flag = 0x00;

//     for (int index = 0; index < 13; index++) {
//         uint32_t command_id = 0;

//         command_id |= static_cast<uint32_t>(command_flag) << 24;
//         command_id |= static_cast<uint32_t>(mCanMasterId) << 8;
//         command_id |= static_cast<uint32_t>(mMotorId[index]);

//         mSlaveCommand->motor[index].sts = 0x00;
//         mSlaveCommand->motor[index].rtr = 0x00;
//         mSlaveCommand->motor[index].ide = 0x01;
//         mSlaveCommand->motor[index].id = command_id;
//         mSlaveCommand->motor[index].dlc = 8;

//         mSlaveCommand->motor[index].data[0] = 0x00;
//         mSlaveCommand->motor[index].data[1] = 0x00;
//         mSlaveCommand->motor[index].data[2] = 0x00;
//         mSlaveCommand->motor[index].data[3] = 0x00;
//         mSlaveCommand->motor[index].data[4] = 0x00;
//         mSlaveCommand->motor[index].data[5] = 0x00;
//         mSlaveCommand->motor[index].data[6] = 0x00;
//         mSlaveCommand->motor[index].data[7] = 0x00;
//     }
// }

