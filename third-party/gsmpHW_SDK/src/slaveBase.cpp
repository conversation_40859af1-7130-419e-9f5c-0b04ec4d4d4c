#include "slaveBase.h"

using namespace gsmp;

void slaveBase::parseImuMessages(EtherCAT_RxMsg *rxMessage, uint8_t slaveIdx, bool verbose){
    static bool mImuConnectState = true;            

    if (rxMessage == nullptr) {
        printf("[IMU] hwSlve->parseImuMessages: rxMessage is nullptr\n");
        return;
    }
    if(rxMessage->imu.IMU_Sts > 0){
        printf("[IMU] \033[31m 从站 %d 上IMU通讯丢失\n\033[0m", slaveIdx + 1);
        mImuConnectState = false;
    } 
    else {

        if (!mImuConnectState) {
            printf("[IMU] \033[32m 从站 %d 上IMU通讯恢复\n\033[0m", slaveIdx + 1);
            mImuConnectState = true;
        }     
        /// 解析IMU数据
        for(uint8_t i=0;i<4;i++) {
            mImuState->quat[i] = uint_to_float(rxMessage->imu.IMU_Quat[i],-1,1,16); 
        }
        for(uint8_t i=0;i<3;i++) {
            mImuState->accel[i] = uint_to_float(rxMessage->imu.IMU_Accel[i],-30,30,16);    
            mImuState->gyro[i] = uint_to_float(rxMessage->imu.IMU_Gyro[i],-2000,2000,24);           
        }      

        if(verbose) {    
            printf("[IMU] quad:  %0.4f,  %0.4f, %0.4f, %0.4f\n", mImuState->quat[0],mImuState->quat[1],mImuState->quat[2],mImuState->quat[3]);     
            printf("[IMU] accel: %0.4f,  %0.4f, %0.4f\n", mImuState->accel[0],mImuState->accel[1],mImuState->accel[2]);               
            printf("[IMU] gyro:  %0.4f,  %0.4f, %0.4f\n", mImuState->gyro[0],mImuState->gyro[1],mImuState->gyro[2]);   
        }                              
    }        
}


void slaveBase::parseBatteryMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose) {
    static bool mBattConnectState = true;
    uint16_t temp;

    if(rxMessage->battery.sts > 0){
        printf("[BATTERY] \033[31m 从站 %d 上电池通讯丢失\n\033[0m", slaveIdx + 1);
        mBattConnectState = false;
    } 
    else {
        if (!mBattConnectState) {
            printf("[BATTERY] \033[32m 从站 %d 上电池通讯恢复\n\033[0m", slaveIdx + 1);
            mBattConnectState = true;
        }
        /// 解析电池数据
        if(rxMessage->battery.id == 0x201){
            // 电压
            temp = (uint16_t)(rxMessage->battery.data[0])<<2;
            temp |= (rxMessage->battery.data[1]>>6);
            mBatteryState->vol = temp*0.1f;
            // 电流
            temp = (uint16_t)(rxMessage->battery.data[1]&0x0F)<<8;
            temp |= rxMessage->battery.data[2];
            mBatteryState->cul = temp*0.1f-100.0f;
            // 电量
            mBatteryState->soc = rxMessage->battery.data[3];
            // 温度
            mBatteryState->temp = rxMessage->battery.data[4]-40;
            // 模式
            mBatteryState->mode = static_cast<batteryMode>(rxMessage->battery.data[5]&0x0F);
            // 故障码          
            if(rxMessage->battery.data[5]>>7){
                mBatteryState->err = batteryErr::PACK_OVER_VOLTAGE;
                printf("\033[31m[BATTERY] battery pack over voltage\033[0m\n");       
            }
            if(rxMessage->battery.data[6] & 0x01 ){
                mBatteryState->err = batteryErr::PACK_UNDER_VOLTAGE;
                printf("\033[31m[BATTERY] battery pack under voltage\033[0m\n");       
            }
            if((rxMessage->battery.data[6]>>1) & 0x01 ){
                mBatteryState->err = batteryErr::CELL_OVER_VOLTAGE;
                printf("\033[31m[BATTERY] battery cell over voltage\033[0m\n");       
            }  
            if((rxMessage->battery.data[6]>>2) & 0x01 ){
                mBatteryState->err = batteryErr::CELL_UNDER_VOLTAGE;
                printf("\033[31m[BATTERY] battery cell under voltage\033[0m\n");       
            }
            if((rxMessage->battery.data[6]>>3) & 0x01 ){
                mBatteryState->err = batteryErr::DISCHARG_OVER_CURRENT;
                printf("\033[31m[BATTERY] battery discharge over current\033[0m\n");       
            }
            if((rxMessage->battery.data[6]>>4) & 0x01 ){
                mBatteryState->err = batteryErr::CHARG_OVER_CURRENT;
                printf("\033[31m[BATTERY] battery charge over current\033[0m\n");       
            }
            if((rxMessage->battery.data[6]>>5) & 0x01 ){
                mBatteryState->err = batteryErr::OUTPUT_SHORT_CIRCUIT;
                printf("\033[31m[BATTERY] battery output short circuit\033[0m\n");       
            }
            if((rxMessage->battery.data[6]>>6) & 0x01 ){
                mBatteryState->err = batteryErr::PACK_OVER_TEMP;
                printf("\033[31m[BATTERY] battery pack over temperature\033[0m\n");       
            } 
            if((rxMessage->battery.data[6]>>7) & 0x01 ){
                mBatteryState->err = batteryErr::PACK_UNDER_TEMP;
                printf("\033[31m[BATTERY] battery pack under temperature\033[0m\n");       
            }  
        }

        if(verbose){
            printf("[BATTERY] vol=%0.1f, cur:%0.1f, soc=%d, tmp:%d\n",mBatteryState->vol,mBatteryState->cul,mBatteryState->soc,mBatteryState->temp);
        }
    }
};        