/*!
 * <AUTHOR>
 * @date 2025-2-14
 * @file masterSerial.cpp
 * @brief Provide a c++ api of Serial Master
 */


//#include <chrono>
#include "masterSerial.h"

using namespace gsmp;


/************************************ Class Member Function *************************************/
/*!
 * @brief message checksum
 * @param data : input
 * @param len : length (in 16-bit words)
 * @return checksum
 */
// uint16_t masterSerial::xor_checksum(uint16_t *data, size_t len) {
//   uint16_t t = 0;
//   for (size_t i = 0; i < len; i++) t = t ^ data[i];
//   return t;
// }

void masterSerial::bindWithSlave(std::shared_ptr<slaveBase> Slave) {
//    static int boundSlaveCount = 0;
    printf("[Serial] Already bind %d Slaves.\n", mSlaveCount+1);
    mSlaveContainer.push_back(Slave);
    mSlaveCount++;
}

bool masterSerial::init(const std::string& port_,int baudrate_) {
    if (port_.empty()) {
        printf("[Serial] masterSerial::init() port is empty!\n");
        return false;
    }

    if (mRxMessage != nullptr || mTxMessage != nullptr) {
        printf("[Serial] masterSerial::init() repeated init!\n");
        return false;
    }

    if (mSlaveContainer.empty()) {
        printf("[Serial] no Slave was bound, please call masterSerial::bindWithSlave first!\n");
        return false;
    }

    mExpectedSlaveNums = mSlaveContainer.size();
    mWorkingState = new masterState[mExpectedSlaveNums];
   
    static int serialCount = 0;
    printf("[Serial] Initializing serial %d with port %s...\n", serialCount, port_.c_str()); 
    int tryCount = 5;
    // serial::Serial serial;
    auto serial_ptr = std::make_unique<serial::Serial>();
    serial_ptr->setPort(port_);
    serial_ptr->setBaudrate(baudrate_);
    serial::Timeout to = serial::Timeout::simpleTimeout(1000);
    serial_ptr->setTimeout(to);
    while (serial_ptr->isOpen() == false && --tryCount >= 0) {
        try {    
            serial_ptr->open();
        }
        catch (serial::IOException &e) {
            printf("[Serial] Attempting to open serial %d, try %d of 5.\n", serialCount, 5-tryCount);            
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }
    }

    if (serial_ptr->isOpen()) {
        serials_.push_back(std::move(serial_ptr));
        // serials_.push_back(std::make_unique<serial::Serial>(std::move(serial)));

        mTxMessage = new serialTxMsg[mExpectedSlaveNums];
        mRxMessage = new serialRxMsg[mExpectedSlaveNums];

        printf("[Serial] Expected %d serials, initialize serial %d successfully.\n", mExpectedSlaveNums, serialCount); 
        mWorkingState[serialCount++] = masterState::NORMAL;

        return true;
    }
    else {
        printf("\033[31m[Serial] initialize serial %d failed.\n\033[0m", serialCount);
        mWorkingState[serialCount++] = masterState::ALL_DISCONNECT;
        return false;
    }
}

// void masterSerial::run(){
//     mRecvThread = std::thread([this]() { this->recv(); });
// }

void masterSerial::send(uint8_t slave){
    std::unique_lock<std::mutex> lock(mSendMutex);
    if (mSlaveContainer.empty()) {
        printf("[Serial] No slave was bound, please call masterSerial::bindWithSlave first or check serial connection!\n");
        return;
    }    

    // size_t slave_count = mSlaveContainer.size();
    // for (uint8_t slave = 0; slave < slave_count; ++slave) {
    slave = (slave >= mExpectedSlaveNums) ? (mExpectedSlaveNums-1) : slave;

    serialTxMsg *serialCommand = mSlaveContainer[slave]->updateSerialCommand();

    memcpy(mTxMessage[slave].sendData, serialCommand->sendData, serialCommand->length);

    if(serials_[slave]->isOpen()){
        serials_[slave]->write(mTxMessage[slave].sendData,serialCommand->length);
    }
    // }

}

void masterSerial::recv(uint8_t slave,bool verbose){
    std::unique_lock<std::mutex> lock(mRecvMutex);
    if (mSlaveContainer.empty()) {
        printf("[Serial] No slave was bound, please call masterSerial::bindWithSlave first or check serial connection!\n");
        return;
    }    

    // size_t slave_count = mSlaveContainer.size();
    // for (uint8_t slave = 0; slave < slave_count; ++slave) {
    slave = (slave >= mExpectedSlaveNums) ? (mExpectedSlaveNums-1) : slave;  
    uint16_t buffLen = sizeof(mRxMessage[slave].recvData);
    uint16_t len = serials_[slave]->available();
    // printf("buffLen = %d,lem = %d \n",buffLen,len);
    if(serials_[slave]->isOpen() && len > 0){
        /// read data from serial
        len = (len <= buffLen)? len : buffLen; // 
        serials_[slave]->read(mRxMessage[slave].recvData,len); 
    }

    /// 解析串口1 imu数据
    if (slave == 0) {
        mSlaveContainer[slave]->parseImuMessages(&mRxMessage[slave],len, verbose);
    }

    /// 解析其他串口数据
    if (slave == 1) {
        // to do
    }
    // }       
    
}

masterSerial::masterSerial(): mSlaveCount(0),
                              mExpectedSlaveNums(0){                              
    mSlaveContainer.clear();
    mTxMessage = nullptr;
    mRxMessage = nullptr;
    mWorkingState = nullptr;
}

masterSerial::~masterSerial() {
    /// 关闭所有打开的串口
    for (auto& serial : serials_) {
        if (serial->isOpen()) {
            serial->close();
        }
    }

    delete [] mTxMessage;
    delete [] mRxMessage;
    delete [] mWorkingState;
    mSlaveContainer.clear();

    // if (mRecvThread.joinable()) {
    //     mRecvThread.join();
    // }
}

uint8_t masterSerial::getSlaveCount() const {
    return mSlaveCount;
}

masterState masterSerial::getMasterWorkingState(uint8_t index) {
    std::unique_lock<std::mutex> lock(mStateMutex);
    if (index < serials_.size()) {
       return mWorkingState[index];
    }
    return masterState::ALL_DISCONNECT; 
}

// serial::Serial& masterSerial::getSerial(size_t index) {
//     if (index < serials_.size()) {
//         return serials_[index];
//     }
// }
