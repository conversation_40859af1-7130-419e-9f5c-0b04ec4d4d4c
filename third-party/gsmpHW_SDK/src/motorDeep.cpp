/*!
 * <AUTHOR>
 * @date   2024-1-10
 * @brief Deep Motors sdk
 */

#include <cmath>
#include <chrono>
#include "motorDeep.h"
#include "motorUtils.h"


using namespace gsmp;

motorDeep::motorDeep() : motorBase() {
    /// MCU端电机排列顺序设置的id（can:1->2->3->4）
    /// 3 channels can 
    // mMotorId[0] = 0x02;     //can1               
    // mMotorId[1] = 0x03;                    
    // mMotorId[2] = 0x05;
    // mMotorId[3] = 0x06;
    // mMotorId[4] = 0x08;     //can2
    // mMotorId[5] = 0x09;
    // mMotorId[6] = 0x0B; 
    // mMotorId[7] = 0x0C;                    
    // mMotorId[8] = 0x01;     //can3
    // mMotorId[9] = 0x04;
    // mMotorId[10] = 0x07;
    // mMotorId[11] = 0x0A; 

    /// 4 channels can 
    // mMotorId[0] = 0x01;    //can1                 
    // mMotorId[1] = 0x02;                    
    // mMotorId[2] = 0x03;    
    // mMotorId[3] = 0x01;    //can2
    // mMotorId[4] = 0x02;
    // mMotorId[5] = 0x03;
    // mMotorId[6] = 0x01;    //can3
    // mMotorId[7] = 0x02;                    
    // mMotorId[8] = 0x03;
    // mMotorId[9] = 0x01;    //can4
    // mMotorId[10] = 0x02;
    // mMotorId[11] = 0x03;    

    /// 2 channels can 
    mMotorId[0] = 0x01;    //can1                 
    mMotorId[1] = 0x02;                    
    mMotorId[2] = 0x03;    
    mMotorId[3] = 0x04;    
    mMotorId[4] = 0x05;
    mMotorId[5] = 0x06;
    mMotorId[6] = 0x07;    //can2
    mMotorId[7] = 0x08;                    
    mMotorId[8] = 0x09;
    mMotorId[9] = 0x0A;    
    mMotorId[10] = 0x0B;
    mMotorId[11] = 0x0C;      
}

/// @brief 将反Z字型排列的电机指令按MCU端的电机顺序调整
std::array<motorCommand, 12> motorDeep::adjustMotorCommandIndex(std::array<motorCommand, 12> &legCommand) {
    std::array<motorCommand, 12> mtrCommand;
    ///根据MCU端实际的顺序调整，电机id详细定义见motorDeep()
    for(int i=0;i<MOTOR_NM;++i){
       mtrCommand[i] = legCommand[mMotorId[i]-1];       
    }
    // mtrCommand[0] = legCommand[3];
    // mtrCommand[1] = legCommand[4];
    // mtrCommand[2] = legCommand[5];
    // mtrCommand[3] = legCommand[9];
    // mtrCommand[4] = legCommand[10];
    // mtrCommand[5] = legCommand[11];
    // mtrCommand[6] = legCommand[6];
    // mtrCommand[7] = legCommand[7];
    // mtrCommand[8] = legCommand[8];
    // mtrCommand[9] = legCommand[0];
    // mtrCommand[10] = legCommand[1];
    // mtrCommand[11] = legCommand[2];

    return mtrCommand;
}

/// @brief 将从MCU端接收的电机数据按反Z字型排列调整
std::array<canMsg, 12> motorDeep::adjustMotorDataIndex(masterRxMsg *rxMessage) {
    std::array<canMsg, 12> mtrData;
    for(int i=0;i<MOTOR_NM;++i){
        mtrData[mMotorId[i]-1] = rxMessage->motor[i];
    }
    // mtrData[3] = rxMessage->motor[0];
    // mtrData[4] = rxMessage->motor[1];
    // mtrData[5] = rxMessage->motor[2];
    // mtrData[9] = rxMessage->motor[3];
    // mtrData[10] = rxMessage->motor[4];
    // mtrData[11] = rxMessage->motor[5];
    // mtrData[6] = rxMessage->motor[6];
    // mtrData[7] = rxMessage->motor[7];     
    // mtrData[8] = rxMessage->motor[8];   
    // mtrData[0] = rxMessage->motor[9];
    // mtrData[1] = rxMessage->motor[10];    
    // mtrData[2] = rxMessage->motor[11];   
   
    return mtrData;
}

bool motorDeep::enableAllMotors() {
    uint8_t commandMode = ENABLE_MOTOR;

    for (uint8_t index = 0; index < MOTOR_NM; index++) {
        uint16_t can_id = FormCanId(commandMode, mMotorId[index]);

        mSlaveCommand->motor[index].id = can_id;
        mSlaveCommand->motor[index].sts = 0x00;
        mSlaveCommand->motor[index].rtr = 0x00;
        mSlaveCommand->motor[index].ide = 0x00;
        mSlaveCommand->motor[index].dlc = 0;
    }

    printf("[MotorDeep] Deep motors enable command sent!\n");

    return true;
}

bool motorDeep::disableAllMotors() {
    uint8_t commandMode = DISABLE_MOTOR;

    for (uint8_t index = 0; index < MOTOR_NM; index++) {
        uint16_t can_id = FormCanId(commandMode, mMotorId[index]);

        mSlaveCommand->motor[index].id = can_id;
        mSlaveCommand->motor[index].sts = 0x00;
        mSlaveCommand->motor[index].rtr = 0x00;
        mSlaveCommand->motor[index].ide = 0x00;
        mSlaveCommand->motor[index].dlc = 0;
    }

    printf("[MotorDeep] Deep motors disable command sent!\n");

    return true;
}

void motorDeep::setMotorCommand(std::array<motorCommand, 12> legCommand) {
    uint8_t commandMode = 0;
    static uint16_t cnt = 0;

    std::array<motorCommand, 12> mtrCommand = adjustMotorCommandIndex(legCommand);

    if(cnt<500){
        commandMode = CONTROL_MOTOR;    // 电机运动控制模式
        for (uint8_t motor_index = 0; motor_index < MOTOR_NM; ++motor_index) {
            uint16_t can_id = FormCanId(commandMode, mMotorId[motor_index]);

            mSlaveCommand->motor[motor_index].id = can_id;        // 电机id根据实际情况调整
            mSlaveCommand->motor[motor_index].sts = 0x00;
            mSlaveCommand->motor[motor_index].rtr = 0x00;
            mSlaveCommand->motor[motor_index].ide = 0x00;
            mSlaveCommand->motor[motor_index].dlc = SEND_DLC_CONTROL_MOTOR;

            uint16_t pos_int = float_to_uint(mtrCommand[motor_index].des_pos, POSITION_MIN, POSITION_MAX, SEND_POSITION_LENGTH);     
            uint16_t spd_int = float_to_uint(mtrCommand[motor_index].des_vel, VELOCITY_MIN, VELOCITY_MAX, SEND_VELOCITY_LENGTH);
            uint16_t tau_int = float_to_uint(mtrCommand[motor_index].tau_ff, TORQUE_MIN, TORQUE_MAX, SEND_TORQUE_LENGTH);
            uint16_t kp_int = float_to_uint(mtrCommand[motor_index].kp, KP_MIN, KP_MAX, SEND_KP_LENGTH);
            uint16_t kd_int = float_to_uint(mtrCommand[motor_index].kd, KD_MIN, KD_MAX, SEND_KD_LENGTH);

            mSlaveCommand->motor[motor_index].data[0] = pos_int;
            mSlaveCommand->motor[motor_index].data[1] = pos_int >> 8;
            mSlaveCommand->motor[motor_index].data[2] = spd_int;
            mSlaveCommand->motor[motor_index].data[3] = ((spd_int >> 8) & 0x3f)| ((kp_int & 0x03) << 6);
            mSlaveCommand->motor[motor_index].data[4] = kp_int >> 2;
            mSlaveCommand->motor[motor_index].data[5] = kd_int;
            mSlaveCommand->motor[motor_index].data[6] = tau_int;
            mSlaveCommand->motor[motor_index].data[7] = tau_int >> 8;
        }
        cnt++;
    }
    else{
        commandMode = GET_STATUS_WORD;    // 每隔1s获取一次电机状态码
        for (uint8_t motor_index = 0; motor_index < MOTOR_NM; ++motor_index) {
            uint16_t can_id = FormCanId(commandMode, mMotorId[motor_index]);

            mSlaveCommand->motor[motor_index].id = can_id;        // 电机id根据实际情况调整
            mSlaveCommand->motor[motor_index].sts = 0x00;
            mSlaveCommand->motor[motor_index].rtr = 0x00;
            mSlaveCommand->motor[motor_index].ide = 0x00;
            mSlaveCommand->motor[motor_index].dlc = 0;
        }
        cnt = 0;
    }
}

void motorDeep::parseMotorMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose) {

    std::array<canMsg, 12> mtrData = adjustMotorDataIndex(rxMessage);

    for (uint8_t i = 0; i < MOTOR_NM; ++i) {
        uint8_t commandMode = (mtrData[i].id >> CAN_ID_SHIFT_BITS) & 0x3f;    
        uint8_t errCode = mtrData[i].data[0];   
        if(mtrData[i].sts > 0){
            printf("[MotorDeep] \033[31m 从站 %d 上的 %d 号电机通讯丢失\n\033[0m", slaveIdx + 1, i + 1);
            mMotorConnectState[i] = false;
        } 
        else {
            if (!mMotorConnectState[i]) {
                printf("[MotorDeep] \033[32m 从站 %d 上的 %d 号电机通讯恢复\n\033[0m", slaveIdx + 1,  i + 1);
                mMotorConnectState[i] = true;
            }
            ///解析电机数据
            switch (commandMode)
                {
                    case ENABLE_MOTOR:
                        printf("[MotorDeep] Motor with index: %d enable success\r\n", i + 1);
                        break;

                    case DISABLE_MOTOR:
                        printf("[MotorDeep] Motor with index: %d disable success\r\n", i + 1);
                        break;

                    case SET_HOME:
                        printf("[MotorDeep] Motor with index: %d set zero point success\r\n", i + 1);
                        break;

                    case ERROR_RESET:
                        printf("[MotorDeep] Motor with index: %d clear error success\r\n", i + 1);
                        break;

                    case CONTROL_MOTOR:
                        UintsToFloats(mtrData,i);
                        break;    

                    case GET_STATUS_WORD:
                        if (errCode&0x01) {
                            printf("[MotorDeep] \033[31m 从站 %d 上的 %d 号电机电压过高\n\033[0m", slaveIdx + 1, i + 1);
                            mMotorStates[i].err = motorErr::OVER_VOLTAGE;
                        }

                        if (errCode&0x02) {
                            printf("[MotorDeep] \033[31m 从站 %d 上的 %d 号电机电压过低\n\033[0m", slaveIdx + 1,  i + 1);
                            mMotorStates[i].err = motorErr::UNDER_VOLTAGE;
                        }

                        if (errCode&0x04) {
                            printf("[MotorDeep] \033[31m 从站 %d 上的 %d 号电机电流过大\n\033[0m", slaveIdx + 1,  i + 1);
                            mMotorStates[i].err = motorErr::OVER_CURRENT;
                        }

                        if ((errCode&0x08) | (errCode&0x10)) {
                            printf("[MotorDeep] \033[31m 从站 %d 上的 %d 号电机温度过高\n\033[0m", slaveIdx + 1,  i + 1);
                            mMotorStates[i].err = motorErr::OVER_HEAT;
                        }
                        break;

                    default:
                        //printf("[motorDeep] Received a frame not fitting into any cmd\r\n");
                        break;
                }

            if(verbose){
                printf("[MotorDeep] motor%2d: pos=%-4.2f, vel=%-4.2f, cur=%-4.2f, tmp=%-3.f\n",i+1,mMotorStates[i].pos, mMotorStates[i].vel, mMotorStates[i].tau, mMotorStates[i].temp);
            }
       }
    }
}

void motorDeep::setMotorImpedance() {
    std::array<motorCommand, 12> command_kd;
    float passive_kd = 2.0f;//(float)(KD_MAX/2);

    for (uint8_t i = 0; i < MOTOR_NM; ++i) {
        command_kd[i].kd = passive_kd;
    }

    motorDeep::setMotorCommand(command_kd);
}

//Form the can id with motor_id and cmd
uint16_t motorDeep::FormCanId(uint8_t motor_mode,uint8_t motor_id){
    return (motor_mode << CAN_ID_SHIFT_BITS) | motor_id;
}

//Transform the uint data in can protocol into float data in MotorDATA
void motorDeep::UintsToFloats(std::array<canMsg,12> &mtrData,uint8_t i)
{
    const ReceivedMotionData *pcan_data = (const ReceivedMotionData*)(mtrData[i].data);
    mMotorStates[i].pos = uint_to_float(pcan_data->position, POSITION_MIN, POSITION_MAX, RECEIVE_POSITION_LENGTH);
    mMotorStates[i].vel = uint_to_float(pcan_data->velocity, VELOCITY_MIN, VELOCITY_MAX, RECEIVE_VELOCITY_LENGTH);
    mMotorStates[i].tau = uint_to_float(pcan_data->torque, TORQUE_MIN, TORQUE_MAX, RECEIVE_TORQUE_LENGTH);
    bool temp_flag = (bool)pcan_data->temp_flag;
    if(temp_flag == kMotorTempFlag){
        mMotorStates[i].temp = uint_to_float(pcan_data->temperature, MOTOR_TEMP_MIN, MOTOR_TEMP_MAX, RECEIVE_TEMP_LENGTH);
    }
    else{
        mMotorStates[i].temp = uint_to_float(pcan_data->temperature, DRIVER_TEMP_MIN, DRIVER_TEMP_MAX, RECEIVE_TEMP_LENGTH);
    }
}




// //Fill in can frame with MotorCMD
// void motorDeep::MakeSendFrame(uint8_t motor_cmd,const motorCommand *TxData){
//     for (size_t i = 0; i < MOTOR_NM; i++)
//     {  
//         uint8_t motor_id = i+1;
//         TxMessage.motor[i].id = FormCanId(motor_cmd, motor_id);
//         switch (motor_cmd)
//         {
//             case ENABLE_MOTOR:
//                 TxMessage.motor[i].dlc = SEND_DLC_ENABLE_MOTOR;
//                 break;

//             case DISABLE_MOTOR:
//                 TxMessage.motor[i].dlc = SEND_DLC_DISABLE_MOTOR;
//                 break;

//             case SET_HOME:
//                 TxMessage.motor[i].dlc = SEND_DLC_SET_HOME;
//                 break;

//             case ERROR_RESET:
//                 TxMessage.motor[i].dlc = SEND_DLC_ERROR_RESET;
//                 break;

//             case CONTROL_MOTOR:
//                 TxMessage.motor[i].dlc = SEND_DLC_CONTROL_MOTOR;
//                 FloatsToUints(TxData, TxMessage.motor[i].data);
//                 break;

//             case GET_STATUS_WORD:
//                 TxMessage.motor[i].dlc = SEND_DLC_GET_STATUS_WORD;
//                 break;

//             default:
//                 break;
//         }      
//     }
// }

// //Fill in MotorDATA with can frame received
// void motorDeep::ParseRecvFrame(motorState *RxData){
//     uint32_t frame_id = RxMessage.motor[0].id;
//     uint32_t cmd = (frame_id >> CAN_ID_SHIFT_BITS) & 0x3f;
//     uint32_t motor_id = frame_id & 0x0f;
//     RxData->motor_id_ = motor_id;
//     RxData->cmd_ = cmd;
//     switch (cmd)
//     {
//         case ENABLE_MOTOR:
//             printf("[INFO] Motor with id: %d enable success\r\n", (uint32_t)motor_id);
//             break;

//         case DISABLE_MOTOR:
//             printf("[INFO] Motor with id: %d disable success\r\n", (uint32_t)motor_id);
//             break;

//         case SET_HOME:
//             printf("[INFO] Motor with id: %d set zero point success\r\n", (uint32_t)motor_id);
//             break;

//         case ERROR_RESET:
//             printf("[INFO] Motor with id: %d clear error success\r\n", (uint32_t)motor_id);
//             break;

//         case CONTROL_MOTOR:
//             UintsToFloats(RxData);
//             break;    

//         case GET_STATUS_WORD:
//             RxData->error_ = (RxMessage.motor[0].data[0] << 8) | RxMessage.motor[0].data[1];
//             break;

//         default:
//             printf("[WARN] Received a frame not fitting into any cmd\r\n");
//             break;
//     }
// }