{"files.associations": {"*.tcc": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "bit": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "source_location": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "variant": "cpp", "bitset": "cpp", "any": "cpp", "ranges": "cpp", "*.ipp": "cpp"}, "python.autoComplete.extraPaths": ["/opt/ros/jazzy/lib/python3.12/site-packages", "/opt/openrobots/lib/python3.8/site-packages", ""], "python.analysis.extraPaths": ["/opt/ros/jazzy/lib/python3.12/site-packages", "/opt/openrobots/lib/python3.8/site-packages", ""]}