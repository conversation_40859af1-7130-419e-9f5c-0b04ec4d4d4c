/*!
 * <AUTHOR>
 * @date 2023-8-9
 * @email <EMAIL>
 * @brief Provide a template class of Motor as ethercat Slave
 */

#pragma once

#include <array>
#include <chrono>
#include "slaveBase.h"

namespace gsmp {

    #define MOTOR_NM 12  //电机数量

    class motorBase : public slaveBase {
    public:
        motorBase() = default;;

        ~motorBase() = default;


        virtual bool enableAllMotors(){return false;};

        virtual bool disableAllMotors(){return false;};       

        virtual void setMotorCommand(std::array<motorCommand, 12> legCommand) = 0;    

        /**
         * 设置电机阻抗模式，只发送kd值*/        
        virtual void setMotorImpedance(){};
    
        virtual void parseMotorMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose) = 0;

        virtual std::array<motorState, 12> getMotorStates(){return mMotorStates;}


    protected:
        // std::array<motorState, 12> mMotorStates{};
        std::array<bool, 12> mMotorConnectState{true};
    };
}
