/*!
 * <AUTHOR>
 * @date 2023-8-9
 * @email <EMAIL>
 * @file masterEtherCat.h
 * @brief Provide a c++ api of EtherCat Master
 */

#pragma once

extern "C" {
#include "ethercat.h"
}

#include "masterBase.h"

namespace gsmp {
    class masterEtherCat : public masterBase {
    public:
        masterEtherCat();       
        ~masterEtherCat();


        // masterEtherCat(const masterEtherCat& master) = delete;
        // masterEtherCat(const masterEtherCat&& master) = delete;

        // masterEtherCat& operator=(const masterEtherCat& master) = delete;       
        // masterEtherCat& operator=(const masterEtherCat&& master) = delete;


        void bindWithSlave(std::shared_ptr<slaveBase> Slave) override;

        bool init(const std::string& devName,int baudrate = 9600) override;

        void send();

        void recv(bool verbose = false);
   
        masterState getMasterWorkingState(uint8_t index) override;

        uint8_t getSlaveCount() const override;

    private:
        unsigned char mInOP;
        char mIOmap[4096]{};

        uint8_t mSlaveCount;
        uint8_t mExpectedWKC;
        uint8_t mExpectedSlaveNums;

        std::atomic_int mWkc{0};
        std::atomic_bool mRunning{false};

        std::mutex mStateMutex;
        masterState mWorkingState;

        std::thread mCheckThread;

        EtherCAT_TxMsg *mTxMessage;
        EtherCAT_RxMsg *mRxMessage;
   
        std::vector<std::shared_ptr<slaveBase>> mSlaveContainer;

    protected:
        bool preInit(const std::string& network);
        void checkLoop();
    };
}