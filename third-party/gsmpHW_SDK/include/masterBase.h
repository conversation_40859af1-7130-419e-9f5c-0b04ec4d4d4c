#pragma once

#include <mutex>
#include <string>
#include <atomic>
#include <thread>
#include <vector>
#include "slaveBase.h"

namespace gsmp {
    enum class masterState: unsigned int {
        NORMAL = 0,
        EXIST_DISCONNECT = 1,
        ALL_DISCONNECT = 2
    };

    class masterBase {
        public:
            masterBase() = default;
            ~masterBase() = default;


            // 删除拷贝构造函数和赋值运算符
            masterBase(const masterBase& master) = delete;
            masterBase& operator=(const masterBase& master) = delete; 

            // 删除移动构造函数和移动赋值运算符
            masterBase(const masterBase&& master) = delete;
            masterBase& operator=(const masterBase&& master) = delete;


            virtual void bindWithSlave(std::shared_ptr<slaveBase> Slave) = 0;

            virtual bool init(const std::string& devName,int baudrate) = 0;

            virtual masterState getMasterWorkingState(uint8_t index) {
                static_cast<void>(index);
                return masterState::ALL_DISCONNECT;
            };

            [[maybe_unused]] virtual uint8_t getSlaveCount() const {return 0;};

            /*!
            * @brief message checksum
            * @param data : input
            * @param len : length (in 16-bit words)
            * @return checksum */
            uint16_t xor_checksum(uint16_t *data, size_t len) {
                uint16_t t = 0;
                for (size_t i = 0; i < len; i++) t = t ^ data[i];
                return t;
            }

            // virtual void send(){};

            // virtual void recv(bool verbose = false){};
            
            // virtual void transfer(bool verbose = false){};  //用于同时收发
        };
}