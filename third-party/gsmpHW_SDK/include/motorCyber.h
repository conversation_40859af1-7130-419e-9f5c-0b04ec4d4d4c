#pragma once

#include <vector>
#include "motorBase.h"

#define PI 3.1415926

namespace gsmp {

    #define P_MIN  -12.56f
    #define P_MAX  12.56f
    #define V_MIN  -15.0f
    #define V_MAX  15.0f
    #define KP_MIN 0.0f
    #define KP_MAX 5000.0f
    #define KD_MIN 0.0f
    #define KD_MAX 100.0f
    #define T_MIN  -120.0f
    #define T_MAX  120.0f

    class motorCyber : public motorBase {
    public:
        motorCyber();

        ~motorCyber() = default;
        

        bool enableAllMotors() override;

        bool disableAllMotors() override;

        void parseMotorMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose) override;

        void setMotorCommand(std::array<motorCommand, 12> legCommand) override;

        void setMotorImpedance() override;

        // void getMotorsCanID();

    protected:
        std::array<motorCommand, 12> adjustMotorCommandIndex(std::array<motorCommand, 12> &legCommand);
        std::array<canMsg, 12> adjustMotorDataIndex(masterRxMsg *rxMessage);  

        uint8_t mCanMasterId;
        uint8_t mMotorId[12];
    };
}