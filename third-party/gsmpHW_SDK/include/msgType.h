/*
 * @Description:
 * @Author: lihy
 * @Date: 2022-09-20 09:21:54
 * @LastEditTime: 2025-5-14
 */
#pragma once

#include <cstring>
#include <cinttypes>

namespace gsmp {
#pragma pack(push, 1)

    enum class motorErr : unsigned int {
        NO_ERR        = 0,
        OVER_VOLTAGE  = 1,
        UNDER_VOLTAGE = 2,
        OVER_CURRENT  = 3,
        OVER_HEAT     = 4,                
        ENCODER_ERROR = 5,
        SENSER_ERROR  = 6
    };

    enum class batteryMode : unsigned int {
        DEFAULT      = 0,
        STANDBY      = 1,
        DISCHARGING  = 2,
        CHARGING     = 3,
        POWERING_OFF = 4,
        FAULT        = 5
    };

    enum class batteryErr : unsigned int {
        NO_ERR = 0,
        PACK_OVER_VOLTAGE,
        PACK_UNDER_VOLTAGE,
        CELL_OVER_VOLTAGE,
        CELL_UNDER_VOLTAGE,
        DISCHARG_OVER_CURRENT,
        CHARG_OVER_CURRENT,
        OUTPUT_SHORT_CIRCUIT,
        PACK_OVER_TEMP,
        PACK_UNDER_TEMP
    };

    struct canMsg {
        uint8_t dlc;            
        uint8_t sts:4;      
        uint8_t rtr:2;
        uint8_t ide:2;
        uint32_t id;
        uint8_t data[8];
    };

    struct imuMsg {
        uint16_t IMU_Quat[4];
        uint16_t IMU_Accel[3];
        uint32_t IMU_Gyro[3];
        uint16_t IMU_Sts;
    };

    struct servoCmd{ 
        int8_t   targetPos;
        uint8_t  targetVel;
    } ;

    /// @brief F710手柄数据结构体定义
    struct F710_data {
        // 摇杆
        float left_stick_x;
        float left_stick_y;
        float right_stick_x;
        float right_stick_y;
        
        // 扳机键
        float left_trigger;  // 新增
        float right_trigger; // 新增
        
        // 按钮
        bool button_A;
        bool button_B;
        bool button_X;
        bool button_Y;
        bool button_LB;
        bool button_RB;
        bool button_back;
        bool button_start;
        bool button_guide;  // 新增
        bool button_L3;     // 新增
        bool button_R3;     // 新增
        
        // D-Pad
        bool dpad_up;
        bool dpad_down;
        bool dpad_left;
        bool dpad_right;
    };

    /// @brief 关节电机的状态和命令结构体定义，12个电机按反Z字型顺序定义
    typedef struct {                // state, 其中temp = 电机和驱动器温度的较大者
        float pos, vel, tau, temp;
        motorErr err;                            
    } motorState;

    typedef struct {                // command
        float des_pos, des_vel, kp, kd, tau_ff;                  
    } motorCommand;

    typedef struct {
        float vol, cul;
        int8_t temp;
        uint8_t soc;
        batteryMode mode; // mode = POWERING_OFF,代表电池即将关机，系统可以根据此信号进行关机前的准备工作
        batteryErr err;
    } batteryState;

    typedef struct {   
        float quat[4];   // w x y z
        float accel[3];  // x y z  单位：m/s^2
        float gyro[3];   // x y z  单位：deg/s
    } imuState;



    /// @brief 主站和从站数据收发的结构体定义，从CAN1开始转发，需要和MCU顺序一致
    typedef struct {
        struct canMsg motor[12];
        uint8_t battery_cmd;        // 1-电池30s后关机,只用于定制电池
        uint8_t ambient_light_cmd;
        uint16_t reserved[6];
        uint16_t checksum;
    } masterTxMsg;

    typedef struct {
        struct canMsg motor[12];  
        struct canMsg battery;    
        uint16_t checksum;
    } masterRxMsg;

    typedef struct {
        struct canMsg motor[12];
        struct servoCmd servo[2];
        uint16_t reserved[5];        // bms_close imu_calib light_ctrl uwb_req
    } EtherCAT_TxMsg;

    typedef struct {
        struct canMsg motor[12];  
        uint16_t reserved[7];        // batt_info
        struct imuMsg imu;   
    } EtherCAT_RxMsg;    

    /// @brief 串口收发数据定义
    typedef struct {
        // uint8_t imu_calib;          // 陀螺仪零偏标定，设为1后静置1s以上   
        uint8_t length;             // 要发送的数据长度，最大16（可以根据需要修改）
        uint8_t sendData[16];    
    } serialTxMsg;

    typedef struct {
        uint8_t recvData[256];   
    } serialRxMsg;



    // typedef struct {
    //     uint16_t battery_cmd;        // 1-电池30s后关机,只用于定制电池
    //     struct servoCmd servo[2];
    //     uint16_t ambient_light_cmd;
    //     uint16_t checksum;
    // } serial2TxMsg;

    // typedef struct {
    //     struct canMsg battery;    
    //     uint16_t checksum; 
    // } serial2RxMsg;

#pragma pack(pop)
}