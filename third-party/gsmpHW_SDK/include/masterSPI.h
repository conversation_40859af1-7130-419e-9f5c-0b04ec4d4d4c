/*!
 * <AUTHOR>
 * @date 2025-2-14
 * @file masterSPI.h
 * @brief Provide a c++ api of SPI Master
 */

#pragma once

#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <linux/spi/spidev.h>

#include "masterBase.h"

namespace gsmp {
    class masterSPI : public masterBase {
    public:
        masterSPI();
        ~masterSPI();


        // masterSPI(const masterSPI& master) = delete;
        // masterSPI& operator=(const masterSPI& master) = delete; 

        // // 删除移动构造函数和移动赋值运算符
        // masterSPI(const masterSPI&& master) = delete;
        // masterSPI& operator=(const masterSPI&& master) = delete;


        void bindWithSlave(std::shared_ptr<slaveBase> Slave) override;

        bool init(const std::string& devName,int baudrate = 9600) override;

        void transfer(bool verbose = false);

        masterState getMasterWorkingState(uint8_t index = 0) override;

        uint8_t getSlaveCount() const override;

    private:
        uint8_t mSlaveCount;
        uint8_t mExpectedSlaveNums;
        int fd;

//        const char *device{"/dev/spidev0.0"};   // 替换为实际的SPI设备节点，如 /dev/spidev0.0
        uint32_t   speed;              // 设置SPI时钟频率，单位Hz
        uint8_t    bits;               // 设置SPI位宽
        uint32_t   mode;               // 设置SPI模式        

        std::mutex mStateMutex;
        std::mutex mTransferMutex;
        masterState mWorkingState;

        masterTxMsg *mTxMessage;
        masterRxMsg *mRxMessage;

        std::vector<std::shared_ptr<slaveBase>> mSlaveContainer;

    // protected:
    //     uint16_t xor_checksum(uint16_t *data, size_t len);
    };
}