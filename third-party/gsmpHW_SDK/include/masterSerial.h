/*!
 * <AUTHOR>
 * @date 2025-6-25
 * @file masterSerial.h
 * @brief Provide a c++ api of Serial Master
 */

#pragma once

#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <serial/serial.h>

#include "masterBase.h"

namespace gsmp {

    // const int sendBuffer = 32;
    // const int recvBuffer = 256;

    class masterSerial : public masterBase {
    public:
        masterSerial();
        ~masterSerial();


        void bindWithSlave(std::shared_ptr<slaveBase> Slave) override;

        bool init(const std::string& devName,int baudrate) override;

        void send(uint8_t slave);   

        void recv(uint8_t slave, bool verbose = false);

        // void run();

        masterState getMasterWorkingState(uint8_t index) override;

        uint8_t getSlaveCount() const override;

        // 获取指定索引的串口实例
        // serial::Serial& getSerial(size_t index);

    private:
        uint8_t mSlaveCount;
        uint8_t mExpectedSlaveNums;

        std::vector<std::unique_ptr<serial::Serial>> serials_;          
        // std::vector<serial::Serial> serials_;   // 串口实例
        // std::string mPort_;                      // 串口端口
        // int mBaudrate_;                          // 串口波特率   
        // uint8_t mTxMessage[sendBuffer];         // 发送数据缓存
        // uint8_t mRxMessage[recvBuffer];         // 接收数据缓存

        /// 串口1用于imu数据收发
        serialTxMsg *mTxMessage;
        serialRxMsg *mRxMessage;        

        std::mutex mSendMutex;       
        std::mutex mRecvMutex; 
        std::mutex mStateMutex;

        // std::thread mRecvThread;
        // std::thread mSendThread;

        // bool mRunning;

        masterState *mWorkingState;

        std::vector<std::shared_ptr<slaveBase>> mSlaveContainer;

    // protected:
    //     bool preInit(const std::string& devName);
    //     uint16_t xor_checksum(uint16_t *data, size_t len);
    };
}