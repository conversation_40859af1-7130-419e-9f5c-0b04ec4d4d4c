#pragma once

#include <cstdio>
#include <memory>
#include "msgType.h"
#include "motorUtils.h"

namespace gsmp {
    
    class slaveBase {
    public:
        slaveBase() {
            mSlaveCommand = std::make_unique<masterTxMsg>();
            mSerialCommand = std::make_unique<serialTxMsg>();
            mBatteryState  = std::make_unique<batteryState>();
            mImuState      = std::make_unique<imuState>();
        }

        ~slaveBase() = default;


        /*!
         * 更新发送给从站的指令
         */
        masterTxMsg *updateSlaveCommand(){return mSlaveCommand.get();}
        serialTxMsg *updateSerialCommand(){return mSerialCommand.get();}

        /*!
         * 解析从站返回的电机数据
         */
        virtual void parseMotorMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose) {
            static_cast<void>(rxMessage);
            static_cast<void>(slaveIdx);
            static_cast<void>(verbose);
        };
        virtual std::array<motorState, 12> getMotorStates() {return mMotorStates;};


        /*!
         * 解析从站返回的IMU信息,目前只有etherCAT通讯有IMU数据
         */
        void parseImuMessages(EtherCAT_RxMsg *rxMessage, uint8_t slaveIdx, bool verbose);
        virtual void parseImuMessages(serialRxMsg *rxMessage, uint16_t len, bool verbose) {
            static_cast<void>(rxMessage);
            static_cast<void>(len);
            static_cast<void>(verbose);            
        };
        imuState *getImuState(){return mImuState.get();}


        /*!
         * 解析从站返回的电池数据
         */
        void parseBatteryMessages(masterRxMsg *rxMessage, uint8_t slaveIdx, bool verbose);
        batteryState *getBatteryState(){return mBatteryState.get();}


        /**
         * @param powerCmd: ture-电池30s后关机，默认false */
        void setBatteryCommand(bool powerCmd){mSlaveCommand->battery_cmd = powerCmd;} 


        /**
         * @param mode: 氛围灯模式 */
        void setAmbientLightCommand(uint8_t mode){mSlaveCommand->ambient_light_cmd = mode;}   

    protected:
        std::unique_ptr<masterTxMsg> mSlaveCommand;
        std::unique_ptr<serialTxMsg> mSerialCommand;

        std::array<motorState, 12> mMotorStates{};
        std::unique_ptr<batteryState> mBatteryState;
        std::unique_ptr<imuState> mImuState;
    };
}
