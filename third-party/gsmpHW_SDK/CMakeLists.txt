cmake_minimum_required(VERSION 3.14)

project(gsmpHW_SDK
        DESCRIPTION "SDK of Geely Smart Mobile Platform"
        VERSION 1.0.0)

#find_package(catkin REQUIRED)
add_subdirectory(third-party)

# set(COMMUNICATION_TYPE ETHERCAT)      # 通讯类型，目前支持 ETHERCAT 或 SPI
# set(MOTOR_TYPE ROBSTRIDE)             # 电机类型，目前支持 DEEP (云深处)、 ROBSTRIDE (灵足/小米)

# if(NOT DEFINED COMMUNICATION_TYPE)
#     message(FATAL_ERROR "[CMake ERROR] Have not defined COMMUNICATION_TYPE")
# endif()
# if(NOT DEFINED MOTOR_TYPE)
#     message(FATAL_ERROR "[CMake ERROR] Have not defined MOTOR_TYPE")
# endif()

# if(${COMMUNICATION_TYPE} STREQUAL "ETHERCAT")
#     add_definitions(-DCOMMUNICATION_ETHERCAT)
# elseif(${COMMUNICATION_TYPE} STREQUAL "SPI")
#     add_definitions(-DCOMMUNICATION_SPI)
# else()
#     message(FATAL_ERROR "[CMake ERROR] The COMMUNICATION_TYPE is error")
# endif()

# if(${MOTOR_TYPE} STREQUAL "DEEP")
#     add_definitions(-DMOTOR_DEEP)
# elseif(${MOTOR_TYPE} STREQUAL "ROBSTRIDE")
#     add_definitions(-DMOTOR_ROBSTRIDE)
# else()
#     message(FATAL_ERROR "[CMake ERROR] The MOTOR_TYPE is error")
# endif()

file(GLOB gsmpHW_SDK_SRCS src/*.cpp)

## Define directories
#set(SDK_DEVEL_PREFIX ${CATKIN_DEVEL_PREFIX} CACHE STRING "SDK install path")
set(SDK_SRC_PREFIX ${CMAKE_CURRENT_SOURCE_DIR})
set(SDK_DEVEL_PREFIX ${CMAKE_CURRENT_BINARY_DIR}/devel CACHE STRING "SDK install path")
set(SDK_INCLUDE_DIR ${SDK_DEVEL_PREFIX}/include/gsmpHW_SDK)
set(SDK_LIB_DIR ${SDK_DEVEL_PREFIX}/lib)
#set(SDK_DOWNLOAD_DIR ${CMAKE_CURRENT_BINARY_DIR}/download)
#set(SDK_BUILD_DIR ${CMAKE_CURRENT_BINARY_DIR}/build)

## Create directories if they do not exist
file(MAKE_DIRECTORY ${SDK_DEVEL_PREFIX})
file(MAKE_DIRECTORY ${SDK_INCLUDE_DIR})
file(MAKE_DIRECTORY ${SDK_LIB_DIR})
#file(MAKE_DIRECTORY ${SDK_DOWNLOAD_DIR})
#file(MAKE_DIRECTORY ${SDK_BUILD_DIR})


#file(GLOB_RECURSE HEADERS "include/*")
#foreach (HEADER_FILE ${HEADERS})
#    message(STATUS "FOUND HEADER: " ${HEADER_FILE})
#    file(COPY ${HEADER_FILE} DESTINATION ${SDK_INCLUDE_DIR})
#endforeach ()


add_library(gsmpHW_SDK SHARED ${gsmpHW_SDK_SRCS})

add_dependencies(gsmpHW_SDK soem serial)  # 确保被依赖的目标在链接前先构建完成


#target_link_libraries(gsmpHW_SDK m rt pthread soem serial ${catkin_LIBRARIES})
target_link_libraries(gsmpHW_SDK m rt pthread soem serial)


set_target_properties(gsmpHW_SDK PROPERTIES VERSION ${PROJECT_VERSION})

#target_include_directories(gsmpHW_SDK PUBLIC include ${SOEM_INCLUDE_DIRS} ${SERIAL_INCLUDE_DIRS} ${catkin_INCLUDE_DIRS})
target_include_directories(gsmpHW_SDK PUBLIC include ${SOEM_INCLUDE_DIRS} ${SERIAL_INCLUDE_DIRS})


target_compile_options(gsmpHW_SDK PUBLIC -fPIC -std=c++17 -Wno-error=deprecated-declarations -Wno-deprecated-declarations)
set_target_properties(gsmpHW_SDK PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${SDK_LIB_DIR})

## catkin_package(
#        INCLUDE_DIRS ${SDK_INCLUDE_DIR}
#        LIBRARIES gsmpHW_SDK
#)

add_executable(master_test main.cpp)
target_link_libraries(master_test gsmpHW_SDK)