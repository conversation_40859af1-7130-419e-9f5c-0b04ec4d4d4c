h2D4izUwChV3uE2rBwWy_p1Y

v1.0 - 20250410
1 集成spi和etherCAT两种通讯方式
2 目前集成了小米/灵足和云深处电机的通讯


v1.01 - 20250416
1 增加了运控关节序号（反Z字型）和MCU端电机序号的映射（需要根据MCU的具体排序修改映射函数）
    std::array<gsmp::motorCommand, 12> adjustMotorCommandIndex(std::array<motorCommand, 12> &legCommand);
    std::vector<canMsg> adjustMotorDataIndex(masterRxMsg *rxMessage);  
2 etherCAT通讯增加IMU数据的接收和解析


v1.02 - 20250422
1 "ctrl+C"终止程序时给云深处电机发送的kd值过大，改为2.0f

v1.03 - 20250616
1 SPI-CAN通讯增加CRC校验
2 SPI BaudRate add to 10M from 5M

v2.0 - 20250620
1 主站master增加串口通讯
2 集成元生IMU数据的解析功能
