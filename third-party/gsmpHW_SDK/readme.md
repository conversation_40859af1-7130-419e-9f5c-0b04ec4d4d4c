### 修改参数
 # 设置通讯类型和电机类型

    #define COMMUNICATION_ETHERCAT  // 通讯类型，目前支持 ETHERCAT 或 SPI
    #define MOTOR_DEEP              // 电机类型，目前支持 DEEP (云深处)、 ROBSTRIDE (灵足/小米)

 # 在对应的电机头文件中修改电机参数

    #define P_MIN  -12.5f
    #define P_MAX  12.5f
    #define V_MIN  -30.0f
    #define V_MAX  30.0f
    #define KP_MIN 0.0f
    #define KP_MAX 500.0f
    #define KD_MIN 0.0f
    #define KD_MAX 5.0f
    #define T_MIN  -12.0f
    #define T_MAX  12.0f

 注：
    1、根据所设置的通讯类型和电机类型定义对应的宏,用于main.cpp的条件编译
    2、根据宏定义选择相应的头文件

        #ifdef COMMUNICATION_ETHERCAT
            #include "masterEtherCat.h"    //etherCAT通讯
        #else
            #include "masterSPI.h"         //SPI通讯
        #endif

        #ifdef MOTOR_ROBSTRIDE
            #include "motorCyber.h"        //小米&灵足电机
        #endif

        #ifdef MOTOR_DEEP
            #include "motorDeep.h"         //云深处电机
        #endif

    3、根据宏定义声明相应的对象

        #ifdef COMMUNICATION_ETHERCAT
            std::shared_ptr<gsmp::etherCatMaster> Master = std::make_shared<gsmp::etherCatMaster>();  //etherCAT通讯
        #else
            std::shared_ptr<gsmp::spiMaster>      Master = std::make_shared<gsmp::spiMaster>();       //SPI通讯  
        #endif

        #ifdef MOTOR_ROBSTRIDE
            std::shared_ptr<gsmp::motorCyber>     Slave  = std::make_shared<gsmp::motorCyber>();      //小米&灵足电机
        #endif

        #ifdef MOTOR_DEEP
            std::shared_ptr<gsmp::motorDeep>     Slave  = std::make_shared<gsmp::motorDeep>();        //云深处电机
        #endif

    4、根据宏定义调用相应的函数,etherCAT单独收发数据,SPI同时收发数据 

        #ifdef COMMUNICATION_ETHERCAT 
                Master->send();

                Master->recv(true);    //参数用于打印收到的数据
        #else
                Master->transfer(true);
        #endif

### 使用说明
1、绑定从站

    Master->bindWithSlave(Slave);

2、初始化从站设备,参数填写设备名

    #ifdef COMMUNICATION_ETHERCAT 
        retInit = Master->init("eno1");              //etherCat初始化
    #else    
        retInit = Master->init("/dev/spidev0.0");    //SPI初始化
    #endif

3、设置命令

    ///设置电机命令
    std::array<gsmp::motorCommand, 12> motorCMD{0};
    for(uint8_t i=0;i<12;++i){
        motorCMD[i].kd = 1.0f;  //for test
    }              
    Slave->setMotorCommand(motorCMD);
    ///设置电池命令,参数true-电池关机
    Slave->setBatteryCommand(false);
    ///设置氛围灯命令,参数为氛围灯模式
    Slave->setAmbientLightCommand(0);

4、收发数据

    #ifdef COMMUNICATION_ETHERCAT 
            Master->send();

            Master->recv(true);     //参数true用于调试,打印收到的数据
    #else
            Master->transfer(true); //参数true用于调试,打印收到的数据
    #endif

5、数据处理,接收解析后的电机/电池数据

    /// 获取电机、电池解析后的数据
    std::array<gsmp::motorState, 12> motorDATA = Slave->getMotorStates();
    gsmp::batteryState *batteryDATA = Slave->getBatteryState();

### 编译

    ```shell
   mkdir build
   cd build
   cmake ..
   make
    ```
### 运行示例程序

    ```shell
   sudo ./master_test
   ```
