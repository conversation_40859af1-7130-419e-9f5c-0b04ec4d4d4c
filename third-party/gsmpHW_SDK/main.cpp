#include <cstdio>
#include <unistd.h>
#include <csignal>
#include <thread>
#include <atomic>

#define COMMUNICATION_SPI  // 通讯类型，目前支持 ETHERCAT 或 SPI
#define MOTOR_DEEP              // 电机类型，目前支持 DEEP (云深处)、 ROBSTRIDE (灵足/小米)



#ifdef COMMUNICATION_ETHERCAT
    #include "masterEtherCat.h"
#else
    #include "masterSPI.h"
    #include "masterSerial.h"
#endif

#ifdef MOTOR_ROBSTRIDE
    #include "motorCyber.h"
#endif

#ifdef MOTOR_DEEP
    #include "motorDeep.h"
#endif

#include "imuYesense.h"


#ifdef COMMUNICATION_ETHERCAT
    std::shared_ptr<gsmp::masterEtherCat> Master = std::make_shared<gsmp::masterEtherCat>();  //etherCAT通讯（电机、电池、IMU）
#else
    std::shared_ptr<gsmp::masterSPI>      Master = std::make_shared<gsmp::masterSPI>();       //SPI通讯（电机、电池） 
    std::shared_ptr<gsmp::masterSerial>   Serial = std::make_shared<gsmp::masterSerial>();    //串口通讯（IMU）
#endif

#ifdef MOTOR_ROBSTRIDE
    std::shared_ptr<gsmp::motorCyber>     Slave  = std::make_shared<gsmp::motorCyber>();      //小米&灵足电机
#endif

#ifdef MOTOR_DEEP
    std::shared_ptr<gsmp::motorDeep>      Slave  = std::make_shared<gsmp::motorDeep>();       //云深处电机
#endif

std::shared_ptr<gsmp::imuYesense>       Imu    = std::make_shared<gsmp::imuYesense>();      //IMU

std::atomic_bool retInit{false};
std::atomic_bool running{false};
std::thread      runSlaveThread;
std::thread      runImuThread;



/*收到Ctrl+C，退出程序*/ 
void ShutDown(int sig){
    running = false;
    if(retInit){
        //退出程序前让电机进入阻尼模式，避免直接摔下
        Slave->setMotorImpedance();
        for(uint8_t i=0;i<3;++i){
#ifdef COMMUNICATION_ETHERCAT
            Master->send();
#else
            Master->transfer(false);
#endif

            usleep(2000);
        }
    }

	printf("\n[MAIN] Exit the program\n");
}

/* 设置实时进程 */
void setProcessScheduler(){ 
    pid_t pid = getpid();
    sched_param param;
    param.sched_priority = sched_get_priority_max(SCHED_FIFO);
    if (sched_setscheduler(pid, SCHED_FIFO, &param) == -1) {
        perror("\033[31m[MAIN] Function setProcessScheduler failed\033[0m");
    }
}

/**
 * @brief 硬件初始化*/
bool hwInit(){

#ifdef COMMUNICATION_ETHERCAT 
    Master->bindWithSlave(Slave);

    if(Master->init("eno1")){              //etherCat初始化  
        return true;
    }
#else    
    Master->bindWithSlave(Slave);
    Serial->bindWithSlave(Imu); 

    if(Master->init("/dev/spidev0.0") &&    //SPI初始化
       Serial->init("/dev/ttyTHS0",460800)) //串口初始化
    {
        return true;
    }

#endif
    return false;
}

void runSlave() {    
    /// 使能所有电机，测试通讯时请勿打开，以免电机出现意外动作
    Slave->disableAllMotors();
    // Slave->enableAllMotors();
    for(uint8_t i=0;i<3;++i){
#ifdef COMMUNICATION_ETHERCAT 
        Master->send();
#else
        Master->transfer(true);
#endif
        usleep(2000);
    }   

    std::array<gsmp::motorCommand, 12> motorCMD{};
    const auto period = std::chrono::milliseconds(2);
    while (running)
    {            
        /// 设置电机、电池、氛围灯指令
        for(uint8_t i=0;i<12;++i){
            motorCMD[i].kd = 1.0f;  //for test
        }              
        Slave->setMotorCommand(motorCMD);
        Slave->setBatteryCommand(false);  //默认false，设置true后电池30s后关机,只用于定制电池
        Slave->setAmbientLightCommand(0); //默认0,氛围灯模式,功能暂未实现

#ifdef COMMUNICATION_ETHERCAT 
        Master->send();
        Master->recv(true);    //参数用于打印收到的数据 

        gsmp::imuState *imuDATA = Slave->getImuState();
#else
        Master->transfer(true);
#endif
        /// 获取电机、电池解析后的数据
        std::array<gsmp::motorState, 12> motorDATA = Slave->getMotorStates();
        gsmp::batteryState *batteryDATA = Slave->getBatteryState();

        std::this_thread::sleep_for(period);
    }
}

void runImu() {
    /// 校准陀螺仪
    Imu->calibrateGyroBias();
    Serial->send(0);
    std::this_thread::sleep_for(std::chrono::seconds(1)); // 等待1s校准完成

    const auto period = std::chrono::milliseconds(5);
    while (running)
    {
        /// 读串口0的数据
        Serial->recv(0,true); 

        /// 获取imu数据
        gsmp::imuState *imuDATA = Imu->getImuState(); 

        std::this_thread::sleep_for(period);
    }
}

void startRun() {
    running = true;
    runSlaveThread = std::thread(runSlave);
    runImuThread = std::thread(runImu);
}

int main()
{
    /*监测Ctrl+C信号,退出程序*/
    struct sigaction sa;
    sa.sa_handler = ShutDown;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    /// 注册信号处理函数
    if (sigaction(SIGINT, &sa, NULL) == -1) {
        perror("\033[31m[MAIN] sigaction \033[0m");
        exit(EXIT_FAILURE);
    }

    setProcessScheduler();

    /// 硬件初始化
    retInit = hwInit();
    if(!retInit){
        printf("[MAIN] 硬件初始化失败，程序退出！\n");
        return 0;
    }

    startRun();

    while(running){
        usleep(1000000);
    }


    if (runSlaveThread.joinable()) {       
        runSlaveThread.join();
    }
    if (runImuThread.joinable()) {       
        runImuThread.join();
    }
    
    return 0;
}
