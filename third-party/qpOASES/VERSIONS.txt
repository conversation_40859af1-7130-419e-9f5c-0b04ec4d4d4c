##
##	This file is part of qpOASES.
##
##	qpOASES -- An Implementation of the Online Active Set Strategy.
##	Copyright (C) 2007-2017 by <PERSON>, <PERSON>,
##	<PERSON> et al. All rights reserved.
##
##	qpOASES is free software; you can redistribute it and/or
##	modify it under the terms of the GNU Lesser General Public
##	License as published by the Free Software Foundation; either
##	version 2.1 of the License, or (at your option) any later version.
##
##	qpOASES is distributed in the hope that it will be useful,
##	but WITHOUT ANY WARRANTY; without even the implied warranty of
##	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
##	See the GNU Lesser General Public License for more details.
##
##	You should have received a copy of the GNU Lesser General Public
##	License along with qpOASES; if not, write to the Free Software
##	Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
##



VERSION HISTORY
===============


3.2 (released on 1st September 2015, last updated on 3rd April 2017):
---------------------------------------------------------------------

+ Addition of SQProblemSchur class implementing Schur complement approach
  for sparse QP problems
+ Introduction of data types int_t and uint_t for integer-valued numbers
+ Minor source code clean-up and bugfixes


3.1 (released on 11th February 2015):
-------------------------------------

+ Addition of C interface
+ Further improved Matlab, Simulink, octave and Python interfaces
+ Possibility to provide pre-computed Cholesky factor of Hessian matrix
+ Source code clean-up and bugfixes


3.0 (released on 29th July 2014, last updated on 17th December 2014):
---------------------------------------------------------------------

+ Addition of unit testing
+ Several bugfixes


3.0beta (released on 16th August 2011, last updated on 4th April 2014):
-----------------------------------------------------------------------

+ Improved ratio tests and termination check for increased reliabilty
+ Introduction of iterative refinement in step determination and 
  drift correction to handle ill-conditioned QPs
+ Introduction of ramping strategy to handle degenerated QPs
+ Addition of far bounds and flipping bounds strategy to handle 
  semi-definite and unbounded QPs more reliably
+ Limited support of sparse QP matrices (also in Matlab interface)
+ Optional linking of LAPACK/BLAS for linear algebra operations
+ Addition of a number of algorithmic options, summarised in an option struct
+ Improved Matlab interface
+ Python interface added
+ Several bugfixes


2.0 (released on 10th February 2009, last updated on 7th December 2009):
------------------------------------------------------------------------

+ Implementation of regularisation scheme for treating QPs with 
  semi-definite Hessians
+ Addition of convenience functionality for Bounds and Constraints 
  objects for specifying guessed active sets
+ Allows to specify a CPU time in addition to an iteration limit
+ Improved efficiency for QPs comprising many constraints
+ Source code cleanup and bugfixes


1.3 (released on 2nd June 2008, last updated on 13th August 2008):
------------------------------------------------------------------

+ Implementation of "initialised homotopy" concept
+ Addition of the SolutionAnalysis class
+ Utility functions for solving test problems in OQP format added
+ Flexibility of Matlab(R) interface enhanced
+ Major source code cleanup
  (Attention: a few class names and calling interfaces have changed!)

  
1.2 (released on 9th October 2007):
-----------------------------------

+ Special treatment of diagonal Hessians
+ Improved infeasibility detection
+ Further improved Matlab(R) interface
+ Extended Simulink(R) interface
+ scilab interface added
+ Code cleanup and several bugfixes


1.1 (released on 8th July 2007):
--------------------------------

+ Implementation of the QProblemB class
+ Basic implementation of the SQProblem class
+ Improved Matlab(R) interface
+ Enabling/Disabling of constraints introduced
+ Several bugfixes


1.0 (released on 17th April 2007):
----------------------------------

Initial release.



##
##	end of file
##
