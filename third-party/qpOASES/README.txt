﻿##
##	This file is part of qpOASES.
##
##	qpOASES -- An Implementation of the Online Active Set Strategy.
##	Copyright (C) 2007-2017 by <PERSON>, <PERSON>,
##	<PERSON> et al. All rights reserved.
##
##	qpOASES is free software; you can redistribute it and/or
##	modify it under the terms of the GNU Lesser General Public
##	License as published by the Free Software Foundation; either
##	version 2.1 of the License, or (at your option) any later version.
##
##	qpOASES is distributed in the hope that it will be useful,
##	but WITHOUT ANY WARRANTY; without even the implied warranty of
##	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. 
##	See the GNU Lesser General Public License for more details.
##
##	You should have received a copy of the GNU Lesser General Public
##	License along with qpOASES; if not, write to the Free Software
##	Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
##



INTRODUCTION
============

qpOASES is an open-source C++ implementation of the recently proposed 
online active set strategy, which was inspired by important observations 
from the field of parametric quadratic programming (QP). It has several 
theoretical features that make it particularly suited for model predictive 
control (MPC) applications. Further numerical modifications have made 
qpOASES a reliable QP solver, even when tackling semi-definite, ill-posed or 
degenerated QP problems. Moreover, several interfaces to third-party software 
like ​Matlab or ​Simulink are provided that make qpOASES easy-to-use even for 
users without knowledge of C/C++.



GETTING STARTED
===============

1. For installation, usage and additional information on this software package 
   see the qpOASES User's Manual located at doc/manual.pdf or check its
   source code documentation!


2. The file LICENSE.txt contains a copy of the GNU Lesser General Public 
   License (v2.1). Please read it carefully before using qpOASES!


3. The whole software package can be obtained from 

       http://www.qpOASES.org/ or
	   https://projects.coin-or.org/qpOASES/

   On this webpage you will also find further support such as a list of 
   questions posed by other users.



CONTACT THE AUTHORS
===================

If you have got questions, remarks or comments on qpOASES, it is strongly 
encouraged to report them by creating a new ticket at the qpOASES webpage.
In case you do not want to disclose your feedback to the public, you may
send an e-mail to

        <EMAIL>

Finally, you may contact one of the main authors directly:

        Hans Joachim Ferreau, <EMAIL>
        Andreas Potschka,     <EMAIL>
        Christian Kirches,    <EMAIL>

Also bug reports, source code enhancements or success stories are most welcome!



##
##	end of file
##
