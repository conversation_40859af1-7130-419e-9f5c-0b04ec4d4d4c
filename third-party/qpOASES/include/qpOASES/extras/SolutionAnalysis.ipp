/*
 *	This file is part of qpOASES.
 *
 *	qpOASES -- An Implementation of the Online Active Set Strategy.
 *	Copyright (C) 2007-2017 by <PERSON>, <PERSON>,
 *	<PERSON> et al. All rights reserved.
 *
 *	qpOASES is free software; you can redistribute it and/or
 *	modify it under the terms of the GNU Lesser General Public
 *	License as published by the Free Software Foundation; either
 *	version 2.1 of the License, or (at your option) any later version.
 *
 *	qpOASES is distributed in the hope that it will be useful,
 *	but WITHOUT ANY WARRANTY; without even the implied warranty of
 *	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *	See the GNU Lesser General Public License for more details.
 *
 *	You should have received a copy of the GNU Lesser General Public
 *	License along with qpOASES; if not, write to the Free Software
 *	Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 *
 */


/**
 *	\file include/qpOASES/extras/SolutionAnalysis.ipp
 *	\author <PERSON> (thanks to <PERSON>)
 *	\version 3.2
 *	\date 2008-2017
 *
 *	Implementation of inlined member functions of the SolutionAnalysis class
 *	designed to perform additional analysis after solving a QP with qpOASES.
 *
 */



/*****************************************************************************
 *  P U B L I C                                                              *
 *****************************************************************************/


BEGIN_NAMESPACE_QPOASES


END_NAMESPACE_QPOASES


/*
 *	end of file
 */
