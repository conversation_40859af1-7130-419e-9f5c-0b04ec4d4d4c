/*! @file Colors.h
 *  @brief Some colors commonly used in the simulator
 *
 * Colors stored for use with OpenGL.
 */

#ifndef PROJECT_COLORS_H
#define PROJECT_COLORS_H

static constexpr float windows2000[] = {220.f / 256.f, 234.f / 256.f,
                                        242.f / 256.f};
static constexpr float disgustingGreen[] = {0.f, 0.2f, 0.2f};
static constexpr float checkerboardDark[] = {0.3f, 0.3f, 0.3f};
static constexpr float checkerboardLight[] = {0.6f, 0.6f, 0.6f};
static constexpr float defaultRobotColor[] = {0.1f, 0.1f, 0.1f};
static constexpr float debugRedColor[] = {0.5f, 0.05f, 0.05f};

#endif  // PROJECT_COLORS_H
