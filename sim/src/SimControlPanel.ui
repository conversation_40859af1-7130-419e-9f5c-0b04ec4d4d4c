<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SimControlPanel</class>
 <widget class="QMainWindow" name="SimControlPanel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1312</width>
    <height>1209</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>SimControlPanel</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QFrame" name="frame_2">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QLabel" name="simulatorStateLabel">
         <property name="text">
          <string>Simulator State: X</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="simulatorConnectedLabel">
         <property name="text">
          <string>Sim Connected?: X</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <widget class="QPushButton" name="startButton">
            <property name="text">
             <string>Start</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QPushButton" name="driverButton">
            <property name="text">
             <string>Driver</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="joystickButton">
            <property name="text">
             <string>Joystick</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QPushButton" name="stopButton">
            <property name="text">
             <string>Stop</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_9">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QRadioButton" name="cheetah3Button">
            <property name="text">
             <string>Cheetah 3</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="miniCheetahButton">
            <property name="text">
             <string>Mini Cheetah</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_10">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QRadioButton" name="simulatorButton">
            <property name="text">
             <string>Simulator</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QRadioButton" name="robotButton">
            <property name="text">
             <string>Robot</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_13">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QPushButton" name="setTerrainButton">
            <property name="text">
             <string>Set Terrain File</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="terrainFileLabel">
            <property name="text">
             <string>TextLabel</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_14">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item>
           <widget class="QCheckBox" name="hide_floor_checkbox">
            <property name="text">
             <string>Hide Floor</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="hide_robot_checkbox">
            <property name="text">
             <string>Hide Simulated Robot</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Simulator Control Parameters</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTableWidget" name="simulatorTable">
         <property name="rowCount">
          <number>1</number>
         </property>
         <property name="columnCount">
          <number>2</number>
         </property>
         <row/>
         <column/>
         <column/>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_6">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="1">
           <widget class="QPushButton" name="loadSimulatorButton">
            <property name="text">
             <string>Load</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QPushButton" name="saveSimulatorButton">
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="frame_5">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>Robot Control Parameters</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTableWidget" name="robotTable">
         <property name="rowCount">
          <number>1</number>
         </property>
         <property name="columnCount">
          <number>2</number>
         </property>
         <row/>
         <column/>
         <column/>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_4">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="1">
           <widget class="QPushButton" name="loadRobotButton">
            <property name="text">
             <string>Load</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QPushButton" name="saveRobotButton">
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="frame_3">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>User Control Parameters</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTableWidget" name="userControlTable">
         <property name="rowCount">
          <number>2</number>
         </property>
         <property name="columnCount">
          <number>0</number>
         </property>
         <row/>
         <row/>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_7">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout_4">
          <item row="0" column="1">
           <widget class="QPushButton" name="loadUserButton">
            <property name="text">
             <string>Load</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QPushButton" name="saveUserButton">
            <property name="text">
             <string>Save</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_8">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="QPushButton" name="goHomeButton">
            <property name="text">
             <string>Go Home</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_12">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QGridLayout" name="gridLayout_6">
          <item row="0" column="0">
           <widget class="QFrame" name="frame_11">
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <item>
              <widget class="QLineEdit" name="kickAngularX">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Ignored" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="kickAngularY">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Ignored" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="kickAngularZ">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Ignored" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="kickLinearX">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="kickLinearY">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="kickLinearZ">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>0</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QPushButton" name="kickButton">
            <property name="text">
             <string>Kick</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1312</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
