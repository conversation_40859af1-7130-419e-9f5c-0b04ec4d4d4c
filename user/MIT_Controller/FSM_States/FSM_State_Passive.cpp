/*============================== Passive ==============================*/
/**
 * FSM State that calls no controls. Meant to be a safe state where the
 * robot should not do anything as all commands will be set to 0.
 */

#include "FSM_State_Passive.h"

/**
 * Constructor for the FSM State that passes in state specific info to
 * the generic FSM State constructor.
 *
 * @param _controlFSMData holds all of the relevant control data
 */
template <typename T>
FSM_State_Passive<T>::FSM_State_Passive(ControlFSMData<T>* _controlFSMData)
    : FSM_State<T>(_controlFSMData, FSM_StateName::PASSIVE, "PASSIVE") {
  // Do nothing
  // Set the pre controls safety checks
  this->checkSafeOrientation = false;

  // Post control safety checks
  this->checkPDesFoot = false;
  this->checkForceFeedForward = false;
}

template <typename T>
void FSM_State_Passive<T>::onEnter() {
  // Default is to not transition
  this->nextStateName = this->stateName;

  // Reset the transition data
  this->transitionData.zero();
}

/**
 * Calls the functions to be executed on each control loop iteration.
 */
template <typename T>
void FSM_State_Passive<T>::run() {
  // Do nothing, all commands should begin as zeros
  testTransition();
}

/**
 * Handles the actual transition for the robot between states.
 * Returns true when the transition is completed.
 *
 * @return true if transition is complete
 */
template <typename T>
TransitionData<T> FSM_State_Passive<T>::testTransition() {
  this->transitionData.done = true;
  return this->transitionData;
}

/**
 * Manages which states can be transitioned into either by the user
 * commands or state event triggers.
 *
 * @return the enumerated FSM state name to transition into
 */
template <typename T>
FSM_StateName FSM_State_Passive<T>::checkTransition() {
  this->nextStateName = this->stateName;
  iter++;

  // Switch FSM control mode
  switch ((int)this->_data->controlParameters->control_mode) {
    case K_PASSIVE:  // normal c (0)
      // Normal operation for state based transitions
      break;

    case K_JOINT_PD:
      // Requested switch to joint PD control
      this->nextStateName = FSM_StateName::JOINT_PD;
      break;

    case K_STAND_UP:
      // Requested switch to joint PD control
      this->nextStateName = FSM_StateName::STAND_UP;
      break;

    case K_RECOVERY_STAND:
      // Requested switch to joint PD control
      this->nextStateName = FSM_StateName::RECOVERY_STAND;
      break;

    default:
      std::cout << "[CONTROL FSM] Bad Request: Cannot transition from "
                << K_PASSIVE << " to "
                << this->_data->controlParameters->control_mode << std::endl;
  }

  // Get the next state
  return this->nextStateName;
}

/**
 * Handles the actual transition for the robot between states.
 * Returns true when the transition is completed.
 *
 * @return true if transition is complete
 */
template <typename T>
TransitionData<T> FSM_State_Passive<T>::transition() {
  // Finish Transition
  this->transitionData.done = true;

  // Return the transition data to the FSM
  return this->transitionData;
}

/**
 * Cleans up the state information on exiting the state.
 */
template <typename T>
void FSM_State_Passive<T>::onExit() {
  // Output current motor states when entering passive mode
  printf("=== Entering PASSIVE Mode - Current Motor States ===\n");
  printf("Leg Order: FR(0), FL(1), HR(2), HL(3)\n");
  printf("Joint Order: Abad, Hip, Knee\n");
  printf("-----------------------------------------------------\n");

  for(int leg = 0; leg < 4; ++leg) {
    const char* leg_names[] = {"FR", "FL", "HR", "HL"};
    printf("Leg %s (ID:%d):\n", leg_names[leg], leg);
    printf("  Position: [%8.5f, %8.5f, %8.5f] rad\n",
           this->_data->_legController->datas[leg].q[0],
           this->_data->_legController->datas[leg].q[1],
           this->_data->_legController->datas[leg].q[2]);
    printf("  Velocity: [%8.5f, %8.5f, %8.5f] rad/s\n",
           this->_data->_legController->datas[leg].qd[0],
           this->_data->_legController->datas[leg].qd[1],
           this->_data->_legController->datas[leg].qd[2]);
    printf("  Torque:   [%8.5f, %8.5f, %8.5f] Nm\n",
           this->_data->_legController->datas[leg].tauEstimate[0],
           this->_data->_legController->datas[leg].tauEstimate[1],
           this->_data->_legController->datas[leg].tauEstimate[2]);
  }
  printf("=====================================================\n");
  // Nothing to clean up when exiting
}

// template class FSM_State_Passive<double>;
template class FSM_State_Passive<float>;
