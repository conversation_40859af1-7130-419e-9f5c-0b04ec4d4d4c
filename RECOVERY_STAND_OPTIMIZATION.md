# RecoveryStand 力矩优化方案

## 问题诊断

### 观察到的问题
1. **高力矩**: 膝关节力矩达到 28+ Nm (正常应 < 15 Nm)
2. **力矩波动**: 特别是 FL 和 HR 腿部力矩不稳定
3. **站立不稳**: 机器人保持蹲下姿势，无法稳定站立

### 根本原因分析
1. **目标位置不当**: `[0.0, 0.8, -1.5]` 可能导致运动学奇异
2. **PD增益过高**: 比例增益导致过度响应
3. **插值过快**: 线性插值导致加速度突变
4. **缺乏力矩限制**: 没有有效的力矩保护机制

## 优化方案

### 1. 目标位置优化
```cpp
// 原始位置 (可能导致奇异)
stand_jpos[i] << 0.0f, 0.8f, -1.5f;

// 优化后 (更保守的角度)
stand_jpos[i] << 0.0f, 0.6f, -1.3f;
```

**改进点**:
- 髋关节角度: `0.8` → `0.6` rad (减少34度)
- 膝关节角度: `-1.5` → `-1.3` rad (减少11度)
- 避免接近运动学奇异点

### 2. PD增益大幅降低
```cpp
// 原始增益 (导致高力矩)
kpMat << 15, 0, 0,
         0, 15, 0,
         0, 0, 8;

// 优化后 (超保守增益)
kpMat << 8, 0, 0,     // Abad: 降低47%
         0, 10, 0,    // Hip: 降低33%
         0, 0, 5;     // Knee: 降低38%
```

**改进点**:
- 所有关节增益显著降低
- 膝关节增益最低，防止高力矩
- 增加阻尼系数提高稳定性

### 3. 平滑插值算法
```cpp
// 原始线性插值
b = (float)curr_iter/(float)max_iter;

// 优化后 S-曲线插值
float t = (float)curr_iter/(float)max_iter;
b = t * t * (3.0f - 2.0f * t);  // 3t² - 2t³
```

**改进点**:
- 消除加速度突变
- 起始和结束时加速度为零
- 减少力矩冲击

### 4. 动作时序延长
```cpp
// 原始时序 (过快)
standup_ramp_iter = 250;    // 0.5秒
standup_settle_iter = 250;  // 0.5秒

// 优化后 (更慢更稳)
standup_ramp_iter = 400;    // 0.8秒 (+60%)
standup_settle_iter = 400;  // 0.8秒 (+60%)
```

### 5. 力矩限制机制
```cpp
// 新增力矩限制
Vec3<T> tauMax;
tauMax << 15.0, 20.0, 15.0;  // [abad, hip, knee] 最大力矩
this->_data->_legController->commands[leg].tauMax = tauMax;
```

### 6. 实时监控系统
```cpp
// 力矩监控和预警
T max_torque = 0;
for(int leg = 0; leg < 4; leg++) {
  for(int joint = 0; joint < 3; joint++) {
    T torque_mag = abs(this->_data->_legController->datas[leg].tauEstimate[joint]);
    if(torque_mag > max_torque) max_torque = torque_mag;
  }
}

if(max_torque > 25.0) {
  printf("[RecoveryStand WARNING] High torque detected: %.2f Nm\n", max_torque);
}
```

## 预期效果

### 力矩改善
- **膝关节力矩**: 28+ Nm → < 15 Nm (降低 >45%)
- **力矩波动**: 显著减少，更平滑的过渡
- **整体稳定性**: 消除力矩尖峰

### 运动质量
- **站立成功率**: 提高稳定站立概率
- **动作平滑度**: S-曲线插值消除突变
- **收敛时间**: 虽然动作变慢，但更可靠

### 安全性
- **力矩限制**: 硬件保护机制
- **实时监控**: 及时发现异常
- **渐进调整**: 避免激进动作

## 测试协议

### 1. 编译和部署
```bash
cd /path/to/Cheetah-Software-0722
make clean && make
```

### 2. 监控指标
- **最大力矩**: 应 < 20 Nm
- **力矩标准差**: 应 < 5 Nm
- **身体高度**: 应稳定在 0.25-0.35m
- **状态转换**: 应无频繁重启

### 3. 日志分析
```bash
# 使用监控脚本分析
python3 recovery_stand_monitor.py robot_log.txt
```

### 4. 渐进测试
1. **仿真测试**: 先在仿真环境验证
2. **台架测试**: 机器人悬空测试关节运动
3. **地面测试**: 实际站立测试
4. **长期测试**: 多次重复验证稳定性

## 进一步优化方向

### 如果力矩仍然过高
1. **进一步降低增益**: kp 可降至 [5, 8, 3]
2. **调整目标位置**: 髋关节可降至 0.5 rad
3. **增加动作时间**: 可延长至 1.0-1.5 秒

### 如果站立不稳定
1. **检查重心位置**: 确保足端位置合理
2. **调整接触检测**: 优化地面接触估计
3. **增加前馈补偿**: 添加重力补偿

### 如果收敛过慢
1. **自适应增益**: 根据误差动态调整增益
2. **分段控制**: 不同阶段使用不同参数
3. **预测控制**: 使用模型预测控制器

## 成功标准

✅ **力矩控制**: 所有关节力矩 < 20 Nm  
✅ **稳定站立**: 机器人能保持站立姿态 > 10 秒  
✅ **平滑过渡**: 无明显的力矩冲击或震荡  
✅ **重复性**: 连续 10 次测试成功率 > 90%  

## 风险评估

🟡 **中等风险**: 动作变慢可能影响响应速度  
🟢 **低风险**: 保守的参数设置提高安全性  
🟢 **可回滚**: 所有修改都有明确的回滚路径
