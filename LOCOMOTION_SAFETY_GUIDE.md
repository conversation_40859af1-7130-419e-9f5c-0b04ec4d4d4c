# MIT_Controller Locomotion 安全检查使用指南

## 1. 安全检查系统概述

### 1.1 安全检查层次结构

```
┌─────────────────────────────────────────┐
│           控制循环开始                    │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│        Pre-Check (控制前检查)            │
│  • 姿态安全检查 (±28.6°)                 │
│  • 系统状态验证                          │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│         执行控制算法                     │
│  • MPC 控制器                           │
│  • WBC 控制器                           │
│  • 步态规划                             │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│       Post-Check (控制后检查)            │
│  • 足端位置限制                          │
│  • 力矩限制                             │
│  • 命令修正                             │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│    Locomotion特定安全检查                │
│  • 腿部运动学检查                        │
│  • 速度限制                             │
│  • 稳定性评估                           │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│         发送控制命令                     │
└─────────────────────────────────────────┘
```

## 2. 安全检查参数配置

### 2.1 姿态安全参数

```cpp
// 在 SafetyChecker.cpp 中
const T max_roll_pitch = 0.5;  // ±28.6度 (弧度)

// 在 FSM_State_Locomotion.cpp 中  
const T max_roll = 40;   // ±40度
const T max_pitch = 40;  // ±40度
```

**建议调整**:
- **保守设置**: 20-25度 (适用于不平地面)
- **标准设置**: 30-40度 (适用于平坦地面)
- **激进设置**: 45-50度 (适用于动态运动)

### 2.2 力矩安全参数

```cpp
// Mini Cheetah 力矩限制
maxLateralForce = 350;   // N (X, Y方向)
maxVerticalForce = 350;  // N (Z方向)

// Cheetah 3 力矩限制  
maxLateralForce = 1800;  // N
maxVerticalForce = 1800; // N
```

### 2.3 运动学安全参数

```cpp
// 足端位置限制
T maxAngle = 1.0472;  // 60度
T maxPDes = _maxLegLength * sin(maxAngle);

// 腿部速度限制
T max_leg_velocity = 9.0;  // m/s

// 身体速度限制 (建议添加)
T max_body_velocity = 5.0;  // m/s
```

## 3. 使用方法

### 3.1 在 Locomotion 状态中启用安全检查

```cpp
// 在构造函数中
FSM_State_Locomotion<T>::FSM_State_Locomotion(ControlFSMData<T>* _controlFSMData) {
    // 启用所有安全检查
    this->turnOnAllSafetyChecks();
    
    // 根据需要关闭特定检查
    this->checkPDesFoot = false;  // WBC控制时关闭
}
```

### 3.2 自定义安全检查

```cpp
// 添加自定义安全检查
bool FSM_State_Locomotion<T>::customSafetyCheck() {
    // 检查电池电压
    if (battery_voltage < 22.0) {
        printf("Low battery: %.1fV\n", battery_voltage);
        return false;
    }
    
    // 检查温度
    for (int motor = 0; motor < 12; motor++) {
        if (motor_temperature[motor] > 80.0) {
            printf("Motor %d overheating: %.1f°C\n", motor, motor_temperature[motor]);
            return false;
        }
    }
    
    return true;
}
```

### 3.3 安全回退机制

```cpp
FSM_StateName FSM_State_Locomotion<T>::checkTransition() {
    // 执行安全检查
    if (!locomotionSafe() || !customSafetyCheck()) {
        // 不安全时强制进入恢复状态
        this->nextStateName = FSM_StateName::RECOVERY_STAND;
        this->transitionDuration = 0.;
        
        // 同时更新遥控器状态
        rc_control.mode = RC_mode::RECOVERY_STAND;
        
        printf("[LOCOMOTION] Safety violation - switching to recovery\n");
        return this->nextStateName;
    }
    
    // 正常状态转换逻辑...
}
```

## 4. 故障处理策略

### 4.1 渐进式响应

```cpp
enum SafetyLevel {
    SAFE = 0,
    WARNING = 1,    // 轻微违规
    CRITICAL = 2,   // 严重违规  
    EMERGENCY = 3   // 紧急情况
};

SafetyLevel assessSafetyLevel() {
    int violation_count = 0;
    
    if (orientation_unsafe) violation_count++;
    if (velocity_unsafe) violation_count++;
    if (force_unsafe) violation_count++;
    
    if (violation_count == 0) return SAFE;
    if (violation_count == 1) return WARNING;
    if (violation_count == 2) return CRITICAL;
    return EMERGENCY;
}

void handleSafetyViolation(SafetyLevel level) {
    switch (level) {
        case WARNING:
            // 降低控制增益
            reduceControlGains(0.8);
            break;
            
        case CRITICAL:
            // 限制速度和力矩
            limitVelocityAndForce(0.5);
            break;
            
        case EMERGENCY:
            // 立即切换到恢复状态
            forceRecoveryMode();
            break;
    }
}
```

### 4.2 自适应安全阈值

```cpp
class AdaptiveSafetyThresholds {
private:
    T base_max_roll = 40.0;
    T current_max_roll;
    
public:
    void updateThresholds(T terrain_roughness, T gait_speed) {
        // 根据地形和步态调整阈值
        T terrain_factor = 1.0 + terrain_roughness * 0.5;
        T speed_factor = 1.0 + gait_speed * 0.2;
        
        current_max_roll = base_max_roll * terrain_factor * speed_factor;
        
        // 限制在合理范围内
        current_max_roll = std::min(current_max_roll, 60.0);
        current_max_roll = std::max(current_max_roll, 20.0);
    }
    
    T getMaxRoll() const { return current_max_roll; }
};
```

## 5. 调试和监控

### 5.1 安全检查日志

```cpp
void logSafetyViolation(const std::string& check_name, T current_value, T threshold) {
    static std::ofstream safety_log("safety_violations.log", std::ios::app);
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    safety_log << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
               << " | " << check_name 
               << " | Current: " << current_value
               << " | Threshold: " << threshold << std::endl;
}
```

### 5.2 实时监控界面

```cpp
void printSafetyStatus() {
    printf("\n=== Locomotion Safety Status ===\n");
    printf("Orientation: Roll=%.1f° Pitch=%.1f° (Max: ±%.1f°)\n", 
           roll_deg, pitch_deg, max_roll);
    printf("Body Velocity: %.2f m/s (Max: %.1f m/s)\n", 
           body_velocity, max_body_velocity);
    printf("Max Leg Velocity: %.2f m/s (Max: %.1f m/s)\n", 
           max_leg_vel, max_leg_velocity);
    printf("Max Force: %.1f N (Max: %.1f N)\n", 
           max_force, max_lateral_force);
    printf("Contact Legs: %d/4\n", contact_count);
    printf("Safety Level: %s\n", safety_level_names[current_safety_level]);
    printf("===============================\n");
}
```

## 6. 最佳实践

### 6.1 安全检查优先级

1. **最高优先级**: 姿态安全 (防止翻倒)
2. **高优先级**: 关节限位 (防止机械损伤)
3. **中优先级**: 速度限制 (防止失控)
4. **低优先级**: 力矩优化 (提高效率)

### 6.2 性能优化

```cpp
// 避免每个周期都进行复杂计算
class OptimizedSafetyChecker {
private:
    int check_counter = 0;
    
public:
    bool performSafetyCheck() {
        // 关键检查每周期执行
        if (!checkCriticalSafety()) return false;
        
        // 非关键检查每10个周期执行一次
        if (++check_counter % 10 == 0) {
            if (!checkNonCriticalSafety()) return false;
        }
        
        return true;
    }
};
```

### 6.3 测试建议

1. **单元测试**: 测试每个安全检查函数
2. **集成测试**: 测试安全检查与控制器的集成
3. **压力测试**: 在极限条件下测试安全机制
4. **故障注入**: 模拟传感器故障和异常情况

## 7. 常见问题和解决方案

### 7.1 误报问题

**问题**: 安全检查过于敏感，频繁触发
**解决**: 
- 调整阈值参数
- 添加滤波器平滑传感器数据
- 使用滑动窗口判断

### 7.2 响应延迟

**问题**: 安全检查响应不够及时
**解决**:
- 提高关键检查的执行频率
- 使用中断机制处理紧急情况
- 优化检查算法的计算效率

### 7.3 状态振荡

**问题**: 在安全边界附近状态频繁切换
**解决**:
- 添加迟滞逻辑
- 使用不同的进入和退出阈值
- 增加状态稳定时间要求
