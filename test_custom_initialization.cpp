#include <iostream>
#include <memory>
#include "robot/include/go2_lowlevel.h"

int main() {
    std::cout << "Testing Custom class initialization..." << std::endl;
    
    try {
        // Test Custom object creation
        std::shared_ptr<Custom> custom = std::make_shared<Custom>();
        std::cout << "✓ Custom object created successfully" << std::endl;
        
        // Test Init method
        custom->Init();
        std::cout << "✓ Custom::Init() called successfully" << std::endl;
        
        // Test lowcmd_publisher pointer
        if (custom->lowcmd_publisher) {
            std::cout << "✓ lowcmd_publisher is not null" << std::endl;
        } else {
            std::cout << "✗ lowcmd_publisher is null!" << std::endl;
            return 1;
        }
        
        // Test lowcmd_publisher get() method
        if (custom->lowcmd_publisher.get() != nullptr) {
            std::cout << "✓ lowcmd_publisher.get() is not null" << std::endl;
        } else {
            std::cout << "✗ lowcmd_publisher.get() is null!" << std::endl;
            return 1;
        }
        
        std::cout << "All tests passed! Custom object should work correctly." << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Exception caught: " << e.what() << std::endl;
        return 1;
    }
}
