find_path(CASADI_INCLUDE_DIR
    casadi/casadi.hpp
    HINTS $ENV{CASADI_PREFIX}/include
    )

if(CASADI_INCLUDE_DIR)
    set(CASADI_INCLUDE_DIR ${CASADI_INCLUDE_DIR} ${CA<PERSON><PERSON>_INCLUDE_DIR}/casadi)
    set(CA<PERSON>DI_FOUND_INCLUDE TRUE)
    message(STATUS "Found CasADi include dir: ${CASADI_INCLUDE_DIR}")
else()
    message(STATUS "Could not find CasADi include dir")
endif()
find_library(CASADI_LIBRARY
    NAMES casadi
    HINTS ${CASADI_INCLUDE_DIR}/../lib $ENV{CASADI_PREFIX}/lib)
if(CASADI_LIBRARY)
    set(CASADI_LIBRARIES ${CASADI_LIBRARIES} ${CASADI_LIBRARY})
endif()

if(CASADI_LIBRARIES)
    message(STATUS "Found CasADi libs: ${CASADI_LIBRARIES}")
else()
    message(STATUS "Could not find CasADi libs")
endif()

if(CASADI_FOUND_INCLUDE AND CASADI_LIBRARIES)
    set(CASADI_FOUND TRUE)
endif()

